{"head": {"status": 200}, "body": {"meta": {"l": ["/checkout"], "h": {"x-aos-model-page": "checkoutPage", "modelVersion": "v2", "x-aos-stk": "Q7bHXyXI-bLvgflL70P2ffzWL3o", "syntax": "graviton"}, "page": {"title": "取货详情 — 安全结账", "metric": {"page": "AOS: Checkout/PickupContact"}, "metricName": "AOS: Checkout/PickupContact", "url": "/shop/checkoutx?_s=PickupContact-init", "htmlUrl": "https://secure8.www.apple.com.cn/shop/checkout?_s=PickupContact-init"}}, "checkout": {"d": {"page": "PickupContact", "acmiFromBag": false, "partnerFinanceFromBag": false}, "c": ["companion<PERSON><PERSON>", "locationConsent", "pickupContact", "session"], "companionBar": {"d": {"showCompanionBar": false, "showSecureCheckoutOverlay": false, "label": "显示订单摘要：", "amount": "RMB 5,399"}, "c": ["orderSummary", "orderDetails"], "orderSummary": {"d": {"editBagLink": null, "showTaxInclusivePrices": true, "continueBagLink": "https://www.apple.com.cn/shop/bag", "items": "1 件商品", "total": "RMB 5,399", "pickupSummary": true, "displayNetOfAllSavings": false, "title": "订单总计", "showEditBag": true, "editLabel": "Edit bag", "editLabelA11y": "Editing your bag", "totalNoShip": "RMB 5,399", "subtotal": "RMB 5,399", "showBuacMessage": true}, "b": {"editBagLink": {"id": "checkout-companionBar-orderSummary-editBagLink", "key": "d.editBagLink", "events": [{"on": "click", "do": [{"set": "^d.showCompanionBar", "to": false}, {"set": "^d.showSecureCheckoutOverlay", "to": true}], "metric": "t.editBagCompanionBar"}]}}, "t": {"editBagCompanionBar": {"microEvents": [{"key": "prop37", "value": "Edit", "slot": "Companion<PERSON>ar", "feature": "Summary"}]}}}, "orderDetails": {"d": {"id": "checkout-companionBar-orderDetails", "title": "订单详情"}, "c": ["fulfillmentCompanionBar", "pickupContactCompanionBar", "billingCompanionBar", "reviewCompanionBar"], "view": "/templates/web/shared/OrderDetails.mustache", "fulfillmentCompanionBar": {"d": {"label": "送货选项"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-fulfillmentCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.fulfillmentCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Fulfillment"}]}}, "c": ["pickupSummary"], "pickupSummary": {"d": {"editPickupLabelA11y": "更改取货详情", "retailDeliveryDates": "今天", "id": "checkout-companionBar-orderDetails-fulfillmentCompanionBar-pickupSummary", "pickupLabel": "店内取货地点：", "store_name": "Apple 昆明", "editPickupLabel": "更改"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-fulfillmentCompanionBar-pickupSummary-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.fulfillmentCompanionBar.pickupSummary", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Pickup"}]}}, "view": "/templates/web/shared/PickupSummary.mustache"}}, "pickupContactCompanionBar": {"d": {"fapiaoType": "仅电子收据", "label": "取货", "fapiaoLabel": "发票：", "pickupContactLabel": "取货联系人：", "editLabel": "更改", "editLabelA11y": "更改取货联系人", "hideDetails": true}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-pickupContactCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.pickupContactCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "PickupContact"}]}}, "p": {"enabled": false}}, "billingCompanionBar": {"d": {"editLabel": "更改", "editLabelA11y": "更改付款详情", "label": "付款方式"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-billingCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.billingCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Payment"}]}}, "p": {"enabled": false}}, "reviewCompanionBar": {"d": {"editLabel": "查看", "editLabelA11y": "View review details", "label": "检查订单"}, "b": {"edit": {"id": "checkout-companionBar-orderDetails-reviewCompanionBar-edit", "events": [{"on": "click", "do": "a.edit", "metric": "t.change"}]}}, "a": {"edit": {"url": "/shop/checkoutx?_a=edit&_m=checkout.companionBar.orderDetails.reviewCompanionBar", "submit": true, "module": "/checkout", "validate": false}}, "t": {"change": {"microEvents": [{"key": "prop37", "value": "Change", "slot": "Companion<PERSON>ar", "feature": "Review"}]}}, "p": {"enabled": false}}}}, "locationConsent": {"d": {"locationConsent": false}, "b": {"locationConsent": {"id": "checkout-locationConsent", "key": "d.locationConsent", "submit": true, "events": [{"on": "set", "do": "a.location-consent"}]}}, "a": {"location-consent": {"url": "/shop/checkoutx?_a=location-consent&_m=checkout.locationConsent", "submit": true, "validate": false}}}, "pickupContact": {"d": {"showTradeInMessageInstructions": false, "thirdPartyDisabledMessage": "此订单不可由他人取货。", "shouldShowChat": true, "continueLabel": "继续选择付款方式", "header": "现在请填写你的取货信息。", "subHeader": "输入你的联系信息：", "enableContinue": true}, "b": {"continue": {"id": "checkout-pickupContact-continue", "name": "continue", "events": [{"on": "click", "do": "a.continueFromPickupContactToBilling", "metric": "t.continueAction"}]}, "pickupContact": {"id": "checkout-pickupContact", "events": [{"on": "load", "metric": "t.signInCustomer"}], "p": {"focused": true}}, "realIdVerification": {"id": "checkout-pickupContact-realIdVerification", "name": "realIdVerification", "events": [{"on": "load", "metric": "t.group1Verification"}]}}, "a": {"continueFromPickupContactToBilling": {"url": "/shop/checkoutx?_a=continueFromPickupContactToBilling&_m=checkout.pickupContact", "submit": true}}, "t": {"continueAction": {"microEvents": [{"key": "eVar21", "value": "transaction.co.checkout.continue.billing.label", "feature": "PickupDetails"}], "macroEvents": {"op": "JOIN", "args": [",", true, {"if": {"get": "d.selfPickupContact.selfContact.address.isDaytimePhoneSelected"}, "then": "event267", "else": {"if": {"get": "d.thirdPartyPickupContact.billingContact.address.isDaytimePhoneSelected"}, "then": "event267", "else": ""}}, {"if": {"op": "EQ", "args": [{"get": "d.pickupContactOptions.selectedPickupOption"}, "SELF"]}, "then": "event142", "else": {"if": {"op": "EQ", "args": [{"get": "d.pickupContactOptions.selectedPickupOption"}, "THIRDPARTY"]}, "then": "event143", "else": ""}}]}}, "signInCustomer": {"macroEvents": "event47"}, "group1Verification": {"macroEvents": "event47"}}, "c": ["selfPickupContact", "footnotes", "eFapiaoSelector", "faq", "eFapiaoSelector"], "selfPickupContact": {"p": {"active": true, "visible": {"get": "p.active"}, "enabled": true}, "c": ["selfContact", "pickupInstructions"], "selfContact": {"c": ["address"], "address": {"d": {"fullDaytimePhone": "", "firstName": "", "mode": "EDIT", "layout": [{"fields": [[{"name": "lastName", "type": "text", "label": "姓氏"}], [{"name": "firstName", "type": "text", "label": "名字"}], [{"name": "emailAddress", "type": "text", "label": "电子邮件地址", "fieldLevelMessage": "我们会将你的电子收据、订单状态更新和所有服务详情发送至此地址。"}], [{"name": "fullDaytimePhone", "type": "text", "label": "联系人手机号码", "labelA11y": "", "fieldLevelMessage": "请检查你输入的手机号码是否正确无误。下单后将无法更改此信息。", "actionName": "contactVerify", "actionLabel": "验证", "attributes": {"prefix": "+86"}}]]}], "showLocationConsent": true, "emailAddress": "", "was": {"emailAddress": "", "fullDaytimePhone": ""}, "lastName": ""}, "b": {"lastName": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-lastName", "key": "d.last<PERSON><PERSON>", "type": "text", "autofill": "family-name", "maxLen": 20, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EMPTY", "args": [{"val": "_"}]}, "then": {"error": "请输入姓氏。"}, "else": {"if": {"op": "GT", "args": [{"op": "LENGTH", "args": [{"val": "_"}]}, 20]}, "then": {"error": "请减少输入的字符数。"}, "else": {"error": ""}}}}}, "firstName": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-firstName", "key": "d.<PERSON><PERSON>", "type": "text", "autofill": "given-name", "maxLen": 14, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EMPTY", "args": [{"val": "_"}]}, "then": {"error": "请输入名字。"}, "else": {"if": {"op": "GT", "args": [{"op": "LENGTH", "args": [{"val": "_"}]}, 14]}, "then": {"error": "请减少输入的字符数。"}, "else": {"error": ""}}}}}, "emailAddress": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-emailAddress", "key": "d.<PERSON><PERSON><PERSON>", "type": "text", "subtype": "email", "autofill": "email", "maxLen": 64, "masked": true, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EMPTY", "args": [{"val": "_"}]}, "then": {"error": "请输入有效的电子邮件地址。"}, "else": {"if": {"op": "NOT", "args": [{"op": "MATCH", "args": ["^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+[.][A-Za-z]{2,}$", {"val": "_"}]}]}, "then": {"error": "请输入有效的电子邮件地址。"}, "else": {"if": {"op": "GT", "args": [{"op": "LENGTH", "args": [{"val": "_"}]}, 64]}, "then": {"error": "请减少输入的字符数。"}, "else": {"error": ""}}}}}}, "fullDaytimePhone": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-fullDaytimePhone", "key": "d.fullDaytimePhone", "type": "text", "subtype": "number", "autofill": "tel", "maxLen": 12, "masked": true, "submit": true, "format": {"inputRegex": "[0-9]|•", "blocks": [{"maxLength": 12}]}, "p": {"editable": true, "valid": {"if": {"op": "EMPTY", "args": [{"val": "_"}]}, "then": {"error": "请输入有效的电话号码。"}, "else": {"if": {"op": "NOT", "args": [{"op": "MATCH", "args": ["^[0-9]{11,12}$", {"val": "_"}]}]}, "then": {"error": "请输入有效的手机号码。"}, "else": {"if": {"op": "GT", "args": [{"op": "LENGTH", "args": [{"val": "_"}]}, 12]}, "then": {"error": "请减少输入的字符数。"}, "else": {"error": ""}}}}}}}, "c": ["verificationModule"], "verificationModule": {"d": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-verificationModule", "verificationToken": "", "authRequired": false}, "b": {"verificationToken": {"id": "checkout-pickupContact-selfPickupContact-selfContact-address-verificationModule-verificationToken", "key": "d.verificationToken", "name": "verificationToken", "submit": true, "events": [{"on": "set", "do": "a.submitVerificationToken"}]}}, "a": {"submitVerificationToken": {"url": "/shop/checkoutx?_a=submitVerificationToken&_m=checkout.pickupContact.selfPickupContact.selfContact.address.verificationModule", "submit": true}}, "view": "/templates/web/shared/VerificationModule.mustache"}}}, "pickupInstructions": {"d": {"pickupType": "SELF", "instructionsHeader": "请注意以下事项：", "instructionsValue": "<ul id=\"rs-pickup-selfContent\">\n<li>取货时，需携带政府颁发的有效身份证件，证件姓名需与此处输入的信息完全一致。</li>\n<li>检查详细信息是否准确无误，下单后信息将无法更改。</li>\n<li>取货时，需出示取货通知或 Apple 钱包中的取货凭证。</li>\n<li>收到提货通知后，您有7天时间提货。如果在7天内未取货，订单将自动取消，退款至您原来的付款方式。</li>\n</ul>", "viewPickupPolicyLink": "<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery#pickup\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery#pickup\" data-autom=\"apple-pickup-policy\" class=\"more\" target=\"_blank\">查看 Apple 取货政策<span class=\"visuallyhidden\">(在新窗口中打开)</span></a>"}}}, "footnotes": {"d": {"tradeInDisclaimerEnabled": false, "bugattiTradeInFootNotes": ["  "], "hasPartnerFinanceInstallment": false, "isInstallmentsUpgradeAvailable": false}}, "eFapiaoSelector": {"d": {"hasFDF": true, "focusFapiao": false, "options": [{"label": "仅电子收据", "sublabel": "", "value": "none"}, {"label": "电子发票 - 个人", "sublabel": "", "moduleKey": "ePersonalFapiao", "value": "e_personal_fdf"}, {"label": "电子发票 - 公司/其他", "sublabel": "", "moduleKey": "eCompanyOtherFapiao", "value": "e_company_fdf"}, {"label": "电子发票 - 增值税专用发票", "sublabel": "", "moduleKey": "eSVATFapiao", "value": "vat_special_fdf"}], "id": "checkout-pickupContact-eFapiaoSelector", "tradeInSubsidy": false, "selectFapiao": "none"}, "b": {"selectFapiao": {"id": "checkout-pickupContact-eFapiaoSelector-selectFapiao", "key": "d.<PERSON><PERSON><PERSON>", "submit": true, "events": [{"on": "set", "metric": "t.selectedEfapiaoOption"}, {"on": "load", "condition": {"op": "EQ", "args": [{"get": "d.<PERSON><PERSON><PERSON>"}, "none"]}, "metric": "t.selectedEfapiaoOption"}]}, "checkout-pickupContact-eFapiaoSelector": {"id": "checkout-pickupContact-eFapiaoSelector"}}, "t": {"selectedEfapiaoOption": {"microEvents": [{"key": "prop37", "value": {"get": "d.<PERSON><PERSON><PERSON>"}, "slot": "ShippingAddress", "feature": "fapiao"}]}}, "c": ["eCompanyOtherFapiao", "ePersonalFapiao", "eSVATFapiao"], "view": "/templates/web/shared/FdfFapiaoSelector.mustache", "eCompanyOtherFapiao": {"d": {"bankAccountName": "", "taxPayerId": "", "id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao", "companyName": "", "mode": "EDIT", "noTaxIdCheck": false, "acceptTermsCheck": false, "layout": [{"fields": [[{"name": "companyName", "type": "text", "label": "公司名称"}], [{"name": "taxPayerId", "type": "text", "label": "纳税人识别号"}], [{"name": "noTaxIdCheck", "type": "checkbox", "label": "我司无纳税人识别号"}], [{"name": "bankAccountName", "type": "text", "label": "银行名称 (选填)"}], [{"name": "bankAccountNumber", "type": "text", "label": "银行账号"}]]}], "bankAccountNumber": ""}, "b": {"bankAccountName": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-bankAccountName", "key": "d.bankAccountName", "type": "text", "maxLen": 35, "submit": true, "optional": {"op": "EMPTY", "args": [{"get": "d.bankAccountNumber"}]}, "p": {"valid": {"if": {"op": "AND", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}, {"op": "!EQ", "args": [{"get": "d.bankAccountNumber"}, ""]}]}, "then": {"error": "如果你提供银行账号，必须同时提供银行名称。"}, "else": {"error": ""}}}}, "taxPayerId": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-taxPayerId", "key": "d.taxPayerId", "type": "text", "maxLen": 20, "submit": true, "optional": {"op": "EQ", "args": [{"get": "d.noTaxIdCheck"}, true]}, "p": {"enabled": {"op": "EQ", "args": [{"get": "d.noTaxIdCheck"}, false]}, "valid": {"if": {"op": "EQ", "args": [{"get": "d.noTaxIdCheck"}, true]}, "then": {"error": ""}, "else": {"if": {"op": "EQ", "args": [{"val": "_"}, ""]}, "then": {"error": "请输入纳税人识别号或勾选“我司无纳税人识别号”以继续。"}, "else": {"if": {"op": "NOT", "args": [{"op": "MATCH", "args": ["^(?=[0-9a-zA-Z]*$)(?:.{15}|.{18}|.{20})$", {"val": "_"}]}]}, "then": {"error": "请输入有效的纳税人识别号。"}, "else": {"error": ""}}}}}}, "companyName": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-companyName", "key": "d.companyName", "type": "text", "maxLen": 35, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EQ", "args": [{"val": "_"}, ""]}, "then": {"error": "请输入你的企业注册名称。"}, "else": {"error": ""}}}}, "bankAccountNumber": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-bankAccountNumber", "key": "d.bankAccountNumber", "type": "text", "maxLen": 28, "submit": true, "optional": {"op": "EMPTY", "args": [{"get": "d.bankAccountName"}]}, "p": {"enabled": {"op": "OR", "args": [{"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.bankAccountName"}]}]}, {"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.bankAccountNumber"}]}]}, {"get": "b.bankAccountNumber.p.focused"}]}, "valid": {"if": {"op": "AND", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}, {"op": "!EQ", "args": [{"get": "d.bankAccountName"}, ""]}]}, "then": {"error": "请输入与此银行关联的账号。"}, "else": {"if": {"op": "AND", "args": [{"op": "NOT", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}]}, {"op": "NOT", "args": [{"op": "MATCH", "args": ["^[0-9]{1,28}$", {"val": "_"}]}]}]}, "then": {"error": "输入的内容无效。"}, "else": {"error": ""}}}}}, "noTaxIdCheck": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-noTaxIdCheck", "key": "d.noTaxIdCheck", "type": "checkbox", "submit": true, "optional": true, "events": [{"on": "set", "condition": {"op": "AND", "args": [{"op": "EQ", "args": [{"val": "_"}, true]}, {"get": "d.taxPayerId"}]}, "do": [{"set": "d.taxPayerId", "to": ""}]}], "p": {"editable": true}}, "acceptTermsCheck": {"id": "checkout-pickupContact-eFapiaoSelector-eCompanyOtherFapiao-acceptTermsCheck", "key": "d.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EQ", "args": [{"val": "_"}, false]}, "then": {"error": "请确认以继续下一步。"}, "else": {"error": ""}}}}}, "p": {"active": {"op": "EQ", "args": [{"get": "^d.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "e_company_fdf"]}, "visible": {"get": "p.active"}}, "view": "/templates/web/shared/ECompanyOtherFapiao.mustache"}, "ePersonalFapiao": {"d": {"id": "checkout-pickupContact-eFapiaoSelector-ePersonalFapiao", "invoiceHeader": "", "mode": "EDIT", "layout": [{"fields": [[{"name": "invoiceHeader", "type": "text", "label": "发票抬头 (选填)"}]]}]}, "b": {"invoiceHeader": {"id": "checkout-pickupContact-eFapiaoSelector-ePersonalFapiao-invoiceHeader", "key": "d.invo<PERSON><PERSON><PERSON><PERSON>", "type": "text", "maxLen": 35, "submit": true, "optional": true, "p": {"enabled": true, "editable": true}}}, "p": {"active": {"op": "EQ", "args": [{"get": "^d.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "e_personal_fdf"]}, "visible": {"get": "p.active"}}, "view": "/templates/web/shared/EPersonalFapiao.mustache"}, "eSVATFapiao": {"d": {"bankAccountName": "", "acceptTermsCheck": false, "taxPayerId": "", "id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao", "companyName": "", "mode": "EDIT", "layout": [{"fields": [[{"name": "companyName", "type": "text", "label": "公司名称"}], [{"name": "taxPayerId", "type": "text", "label": "纳税人识别号"}], [{"name": "bankAccountName", "type": "text", "label": "银行名称 (选填)"}], [{"name": "bankAccountNumber", "type": "text", "label": "银行账号"}]]}], "bankAccountNumber": ""}, "b": {"bankAccountName": {"id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao-bankAccountName", "key": "d.bankAccountName", "type": "text", "maxLen": 35, "submit": true, "optional": {"op": "EMPTY", "args": [{"get": "d.bankAccountNumber"}]}, "p": {"valid": {"if": {"op": "AND", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}, {"op": "!EQ", "args": [{"get": "d.bankAccountNumber"}, ""]}]}, "then": {"error": "如果你提供银行账号，必须同时提供银行名称。"}, "else": {"error": ""}}}}, "acceptTermsCheck": {"id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao-acceptTermsCheck", "key": "d.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EQ", "args": [{"val": "_"}, false]}, "then": {"error": "请确认以继续下一步。"}, "else": {"error": ""}}}}, "taxPayerId": {"id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao-taxPayerId", "key": "d.taxPayerId", "type": "text", "maxLen": 20, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EQ", "args": [{"val": "_"}, ""]}, "then": {"error": "请输入纳税人识别号以继续。"}, "else": {"if": {"op": "NOT", "args": [{"op": "MATCH", "args": ["^(?=[0-9a-zA-Z]*$)(?:.{15}|.{18}|.{20})$", {"val": "_"}]}]}, "then": {"error": "请输入有效的纳税人识别号。"}, "else": {"error": ""}}}}}, "companyName": {"id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao-companyName", "key": "d.companyName", "type": "text", "maxLen": 35, "submit": true, "p": {"editable": true, "valid": {"if": {"op": "EQ", "args": [{"val": "_"}, ""]}, "then": {"error": "请输入你的企业注册名称。"}, "else": {"error": ""}}}}, "bankAccountNumber": {"id": "checkout-pickupContact-eFapiaoSelector-eSVATFapiao-bankAccountNumber", "key": "d.bankAccountNumber", "type": "text", "maxLen": 28, "submit": true, "optional": {"op": "EMPTY", "args": [{"get": "d.bankAccountName"}]}, "p": {"enabled": {"op": "OR", "args": [{"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.bankAccountName"}]}]}, {"op": "NOT", "args": [{"op": "EMPTY", "args": [{"get": "d.bankAccountNumber"}]}]}]}, "valid": {"if": {"op": "AND", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}, {"op": "!EQ", "args": [{"get": "d.bankAccountName"}, ""]}]}, "then": {"error": "请输入与此银行关联的账号。"}, "else": {"if": {"op": "AND", "args": [{"op": "NOT", "args": [{"op": "EMPTY", "args": [{"val": "_"}]}]}, {"op": "NOT", "args": [{"op": "MATCH", "args": ["^[0-9]{1,28}$", {"val": "_"}]}]}]}, "then": {"error": "输入的内容无效。"}, "else": {"error": ""}}}}}}, "p": {"active": {"op": "EQ", "args": [{"get": "^d.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "vat_special_fdf"]}, "visible": {"get": "p.active"}}, "view": "/templates/web/shared/ESVATFapiao.mustache"}}, "faq": {"d": {"faq": [{"question": "我何时可以提取订单商品？", "answer": "当你订购的商品准备就绪时，我们会向你发送通知。现货商品通常可于一小时内在 Apple Store 零售店备好。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 取货</span><span class=\"visuallyhidden\">(在新窗口中打开)</span><span aria-hidden=\"true\" class=\"more\"></span></a>"}, {"question": "我可以到 Apple Store 零售店提取订购的商品吗？ ", "answer": "可以。如果你选择取货，需要在结账时为商品选择取货门店和取货日期。并非所有商品均可使用取货服务。当你订购的商品备好并可取货时，我们会发送短信通知你。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 送货与取货</span><span class=\"visuallyhidden\"> (在新窗口中打开)</span><span aria-hidden=\"true\" class=\"more\"></span></a>"}, {"question": "我何时会收到短信通知？", "answer": "我们会通过短信或 iMessage 信息将最新的送货状态发送至收件人的手机号码。我们将使用联系人电话号码来沟通有关订单或付款方面的事宜。请确保已开启 iMessage 信息。如果订单出现问题，我们也会联系你。通知发送时段为每天上午 8:00 至晚上 9:00。 <br /><br />若要停止接收短信通知，请致电 <span>400-666-8800</span> 联系我们。"}, {"question": "我何时能收到订购的商品？", "answer": "输入所选省份，你可以查看预计送达日期或取货日期。下单后，你会知道最终确认的日期。所有预估信息根据商品供应情况和你选择的送货方式估算得出。<a href=\"https://www.apple.com.cn/shop/help/shipping_delivery\" data-feature-name=\"Astro Link\" data-display-name=\"AOS: help/shipping_delivery\" class=\"icon-wrapper\" target=\"_blank\"><span class=\"icon-copy\">进一步了解 Apple 送货与取货</span><span class=\"visuallyhidden\">(在新窗口中打开)</span><span aria-hidden=\"true\" class=\"more\"></span></a>\n"}, {"question": "我的商品会在取货点保留多久？", "answer": "你的取货时限为 7 天。如果你未能在此时限内取货，我们将取消订单并退款至你的账户。"}], "faqTitle": "取货常见问题解答"}}}, "session": {"d": {"alertMs": "60000", "interactionMs": "300000", "expiredUrl": "https://www.apple.com.cn/shop/sorry/session_expired", "canExtend": true, "ttl": "1190239", "extendSessionUrl": "/shop/checkoutx/session?_a=extendSessionUrl&_m=checkout.session", "extendSession": null}, "b": {"extendSession": {"id": "checkout-session-extendSession", "key": "d.extendSession", "events": [{"on": "click", "do": "a.extendSession"}]}}, "a": {"extendSession": {"url": "/shop/checkoutx/session?_a=extendSession&_m=checkout.session"}}}}}}