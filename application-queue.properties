# 消息队列模式配置示例
# 复制到 application.properties 或使用 --spring.profiles.active=queue 启动

# 基础配置
apple.login.username=<EMAIL>
apple.login.password=your-password
apple.login.headless=true

# 多线程配置
apple.crawler.thread.count=5

# 消息队列特定配置
# 会话刷新间隔（小时）- 0表示禁用定时刷新
apple.session.refresh.interval.hours=24

# 健康检查间隔（分钟）- 0表示禁用健康检查
apple.health.check.interval.minutes=30

# 截图和视频配置
apple.screenshot.dir=${user.home}/Desktop/AppleScreenshots
apple.record.video=true
apple.video.dir=videos

# 重试配置
apple.pickup.retry.count=3

# 网络检测
apple.network.error.detection=true

# 持久化上下文
apple.use.persistent.context=false
