{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1161", "installByDefault": true, "browserVersion": "134.0.6998.35"}, {"name": "chromium-headless-shell", "revision": "1161", "installByDefault": true, "browserVersion": "134.0.6998.35"}, {"name": "chromium-tip-of-tree", "revision": "1304", "installByDefault": false, "browserVersion": "135.0.7021.0"}, {"name": "chromium-tip-of-tree-headless-shell", "revision": "1304", "installByDefault": false, "browserVersion": "135.0.7021.0"}, {"name": "firefox", "revision": "1475", "installByDefault": true, "browserVersion": "135.0"}, {"name": "firefox-beta", "revision": "1471", "installByDefault": false, "browserVersion": "136.0b4"}, {"name": "webkit", "revision": "2140", "installByDefault": true, "revisionOverrides": {"debian11-x64": "2105", "debian11-arm64": "2105", "mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816", "mac12": "2009", "mac12-arm64": "2009", "ubuntu20.04-x64": "2092", "ubuntu20.04-arm64": "2092"}, "browserVersion": "18.4"}, {"name": "ffmpeg", "revision": "1011", "installByDefault": true, "revisionOverrides": {"mac12": "1010", "mac12-arm64": "1010"}}, {"name": "winldd", "revision": "1007", "installByDefault": false}, {"name": "android", "revision": "1001", "installByDefault": false}]}