# 使用 Microsoft Playwright 官方镜像作为基础镜像
FROM mcr.microsoft.com/playwright:latest

# 设置工作目录
WORKDIR /app

# 安装必要工具
RUN apt-get update && \
    apt-get install -y wget unzip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 下载并安装 GraalVM Linux ARM64 版本
RUN wget -O /tmp/graalvm.tar.gz "https://github.com/graalvm/graalvm-ce-builds/releases/download/jdk-21.0.2/graalvm-community-jdk-21.0.2_linux-aarch64_bin.tar.gz" && \
    tar -xzf /tmp/graalvm.tar.gz -C /opt && \
    mv /opt/graalvm-community-jdk-21.0.2 /opt/graalvm && \
    rm /tmp/graalvm.tar.gz

# 设置 Java 环境
ENV JAVA_HOME=/opt/graalvm
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# 验证 Java 安装
RUN java -version

# 复制 JAR 文件
COPY target/applecrawler-0.0.1-SNAPSHOT.jar .

# 创建必要的目录
RUN mkdir -p /app/users /app/AppleScreenshots /app/videos /app/my-apple-session

# 复制配置文件（如果存在）
COPY application.properties* ./
COPY config.json.example.json ./

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV APPLE_LOGIN_HEADLESS=true
ENV APPLE_CRAWLER_THREAD_COUNT=5

# 暴露端口
EXPOSE 8080

# 设置启动命令（使用 JAR）
CMD java $JAVA_OPTS -jar applecrawler-0.0.1-SNAPSHOT.jar server