#!/bin/bash

# Apple Crawler 本地构建 + Docker 部署脚本
# 先在本地用 GraalVM 构建原生镜像，然后构建 Docker 镜像

set -e

echo "🚀 Apple Crawler 本地构建 + Docker 部署"

# 检查本地 GraalVM
echo "🔍 检查本地 GraalVM 环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 未找到 Java，请先安装 GraalVM"
    exit 1
fi

if ! command -v native-image &> /dev/null; then
    echo "❌ 未找到 native-image，请确保 GraalVM 已正确安装"
    exit 1
fi

echo "✅ GraalVM 环境检查通过"
java -version
native-image --version

# 清理之前的构建
echo "🧹 清理之前的构建..."
mvn clean

# 构建 JAR
echo "📦 构建 JAR 文件..."
mvn package -DskipTests

# 构建 Native Image
echo "🏗️  构建 Native Image..."
./mvnw -DskipTests -Pnative native:compile

# 检查构建结果
if [ -f "target/applecrawler" ]; then
    echo "✅ Native Image 构建成功!"
    ls -lh target/applecrawler
elif [ -f "target/applecrawler-0.0.1-SNAPSHOT.jar" ]; then
    echo "✅ JAR 文件构建成功!"
    ls -lh target/applecrawler-0.0.1-SNAPSHOT.jar
else
    echo "❌ 构建失败，未找到可执行文件"
    exit 1
fi

# 构建 Docker 镜像
echo "🐳 构建 Docker 镜像..."
docker-compose build

if [ $? -eq 0 ]; then
    echo "✅ Docker 镜像构建成功!"
    echo ""
    echo "🚀 启动服务:"
    echo "  docker-compose up -d"
    echo ""
    echo "📋 查看日志:"
    echo "  docker-compose logs -f"
else
    echo "❌ Docker 镜像构建失败!"
    exit 1
fi
