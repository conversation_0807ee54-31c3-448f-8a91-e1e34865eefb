# 使用 GraalVM 官方镜像作为基础镜像
FROM ghcr.io/graalvm/graalvm-community:21

# 设置工作目录
WORKDIR /app

# 安装 Maven 和 Playwright 依赖
RUN microdnf install -y maven wget unzip && \
    microdnf clean all

# 安装 Playwright 浏览器
RUN curl -fsSL https://playwright.azureedge.net/builds/driver/playwright-1.51.0-linux.zip -o playwright.zip && \
    unzip playwright.zip && \
    ./playwright-1.51.0-linux/playwright.sh install-deps && \
    ./playwright-1.51.0-linux/playwright.sh install && \
    rm -rf playwright.zip playwright-1.51.0-linux

# 安装 native-image
RUN gu install native-image

# 验证安装
RUN java -version && native-image --version

# 复制 Maven 配置文件
COPY pom.xml .
COPY mvnw .

# 复制源代码
COPY src ./src

# 构建应用（JAR 和 Native Image）
RUN mvn clean package -DskipTests && \
    mvn -Pnative spring-boot:build-image -DskipTests

# 创建必要的目录
RUN mkdir -p /app/users /app/AppleScreenshots /app/videos /app/my-apple-session

# 复制配置文件（如果存在）
COPY application.properties* ./
COPY config.json.example.json ./

# 设置环境变量
ENV JAVA_OPTS="-Xmx2g -Xms1g"
ENV APPLE_LOGIN_HEADLESS=true
ENV APPLE_CRAWLER_THREAD_COUNT=5

# 暴露端口
EXPOSE 8080

# 设置启动命令（优先使用 native image，fallback 到 JAR）
CMD if [ -f "target/applecrawler" ]; then \
        ./target/applecrawler server; \
    else \
        java -jar target/applecrawler-0.0.1-SNAPSHOT.jar server; \
    fi
