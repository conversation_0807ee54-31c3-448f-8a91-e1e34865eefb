version: '3.8'

services:
  applecrawler:
    build:
      context: .
      dockerfile: Dockerfile.playwright-base
      # 使用 Playwright 基础镜像 + GraalVM（推荐，系统依赖齐全）
    container_name: applecrawler-app
    ports:
      - "8080:8080"
    volumes:
      # 挂载用户数据目录
      - ./users:/app/users
      # 挂载截图目录
      - ./AppleScreenshots:/app/AppleScreenshots
      # 挂载视频目录
      - ./videos:/app/videos
      # 挂载会话目录
      - ./my-apple-session:/app/my-apple-session
      # 挂载配置文件（可选，如果需要动态修改配置）
      - ./application.properties:/app/application.properties:ro
    environment:
      # GraalVM 环境变量
      - GRAALVM_HOME=/opt/graalvm
      - JAVA_HOME=/opt/graalvm
      # Java 虚拟机参数（Native Image 模式下这些参数可能不适用）
      - JAVA_OPTS=-Xmx2g -Xms1g
      # Apple 爬虫配置
      - APPLE_LOGIN_HEADLESS=true
      - APPLE_CRAWLER_THREAD_COUNT=5
      # 日志级别
      - LOGGING_LEVEL_COM_CRAWLER_APPLE=DEBUG
      # Native Image 特定配置
      - SPRING_AOT_ENABLED=true
    restart: unless-stopped
    # 如果需要显示浏览器界面，可以添加以下配置
    # environment:
    #   - DISPLAY=:99
    # volumes:
    #   - /tmp/.X11-unix:/tmp/.X11-unix:rw
    networks:
      - applecrawler-network

networks:
  applecrawler-network:
    driver: bridge
