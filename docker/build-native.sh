#!/bin/bash

# GraalVM Native Image 构建脚本
# 用于在本地或 Docker 环境中构建 Apple Crawler 的 Native Image

set -e

echo "🚀 开始构建 GraalVM Native Image..."

# 检查是否在 Docker 环境中
if [ -f /.dockerenv ]; then
    echo "📦 检测到 Docker 环境"
    BUILD_MODE="docker"
else
    echo "💻 检测到本地环境"
    BUILD_MODE="local"
fi

# 设置构建参数
MAVEN_OPTS="-Xmx4g -Xms2g"
NATIVE_BUILD_ARGS="--no-fallback --enable-preview --initialize-at-build-time=com.microsoft.playwright --initialize-at-run-time=com.microsoft.playwright.impl.PlaywrightImpl"

echo "🔧 构建配置:"
echo "  - 模式: $BUILD_MODE"
echo "  - Maven 内存: $MAVEN_OPTS"
echo "  - Native 参数: $NATIVE_BUILD_ARGS"

# 清理之前的构建
echo "🧹 清理之前的构建..."
mvn clean

# 构建 JAR 文件
echo "📦 构建 JAR 文件..."
export MAVEN_OPTS="$MAVEN_OPTS"
mvn package -DskipTests

# 构建 Native Image
echo "🏗️  构建 Native Image..."
mvn -Pnative -DskipTests -Dspring-boot.build-image.skip=true

# 检查构建结果
if [ -f "target/applecrawler" ]; then
    echo "✅ Native Image 构建成功!"
    echo "📁 输出文件: target/applecrawler"
    
    # 显示文件信息
    ls -lh target/applecrawler
    
    # 测试运行
    echo "🧪 测试 Native Image..."
    timeout 10s ./target/applecrawler --help || echo "⚠️  应用启动测试完成"
    
else
    echo "❌ Native Image 构建失败!"
    echo "💡 尝试使用 JAR 模式运行..."
    if [ -f "target/applecrawler-0.0.1-SNAPSHOT.jar" ]; then
        echo "✅ JAR 文件可用: target/applecrawler-0.0.1-SNAPSHOT.jar"
    else
        echo "❌ JAR 文件也不存在!"
        exit 1
    fi
fi

echo "🎉 构建完成!"
