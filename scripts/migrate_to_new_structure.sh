#!/bin/bash

# 数据迁移脚本：从旧结构迁移到新的用户目录结构
echo "🔄 开始迁移数据到新的用户目录结构..."
echo ""

# 创建备份
echo "📦 创建备份..."
if [ -d "users_backup" ]; then
    rm -rf "users_backup"
fi
cp -r users users_backup
echo "✅ 备份已创建: users_backup/"
echo ""

# 迁移用户配置文件
echo "📁 迁移用户配置文件..."
cd users

# 处理每个 .json 文件（排除 apple-auth.json）
for config_file in *.json; do
    if [ "$config_file" != "apple-auth.json" ] && [ -f "$config_file" ]; then
        # 从文件名提取用户名（去掉.json后缀）
        username="${config_file%.json}"
        
        echo "处理用户: $username"
        
        # 创建用户目录
        if [ ! -d "$username" ]; then
            mkdir -p "$username"
            echo "  ✅ 创建目录: $username/"
        fi
        
        # 移动配置文件并重命名为 config.json
        if [ -f "$config_file" ]; then
            mv "$config_file" "$username/config.json"
            echo "  ✅ 移动配置文件: $config_file → $username/config.json"
        fi
    fi
done

cd ..

# 迁移会话文件
echo ""
echo "🔑 迁移会话文件..."

# 检查是否有旧的 sessions 目录
if [ -d "sessions" ]; then
    cd sessions
    for session_file in *.json; do
        if [ -f "$session_file" ]; then
            # 从会话文件名提取用户名
            # 假设格式为 username_session.json
            username="${session_file%_session.json}"
            
            echo "处理会话文件: $session_file → $username"
            
            # 确保用户目录存在
            if [ ! -d "../users/$username" ]; then
                mkdir -p "../users/$username"
                echo "  ✅ 创建目录: users/$username/"
            fi
            
            # 移动会话文件并重命名为 session.json
            mv "$session_file" "../users/$username/session.json"
            echo "  ✅ 移动会话文件: $session_file → users/$username/session.json"
        fi
    done
    cd ..
    
    # 删除空的 sessions 目录
    if [ -z "$(ls -A sessions)" ]; then
        rmdir sessions
        echo "  ✅ 删除空的 sessions 目录"
    fi
else
    echo "  ℹ️ 没有找到 sessions 目录，跳过会话文件迁移"
fi

# 处理根目录下的 apple-auth.json（如果存在的话，这应该是某个用户的会话文件）
echo ""
echo "🔍 处理特殊文件..."

if [ -f "apple-auth.json" ]; then
    echo "发现 apple-auth.json，这可能是 <EMAIL> 用户的会话文件"
    
    # 确保用户目录存在
    if [ ! -d "users/<EMAIL>" ]; then
        mkdir -p "users/<EMAIL>"
        echo "  ✅ 创建目录: users/<EMAIL>/"
    fi
    
    # 如果该用户还没有会话文件，则移动 apple-auth.json
    if [ ! -f "users/<EMAIL>/session.json" ]; then
        mv "apple-auth.json" "users/<EMAIL>/session.json"
        echo "  ✅ 移动会话文件: apple-auth.json → users/<EMAIL>/session.json"
    else
        echo "  ⚠️ 用户已有会话文件，保留 apple-auth.json"
    fi
fi

# 显示新结构
echo ""
echo "📊 新的目录结构:"
echo ""
ls -la users/

echo ""
echo "📋 详细用户目录内容:"
for user_dir in users/*/; do
    if [ -d "$user_dir" ]; then
        echo ""
        echo "📁 $user_dir"
        ls -la "$user_dir"
    fi
done

echo ""
echo "🎉 迁移完成！"
echo ""
echo "📝 迁移总结:"
echo "✅ 每个用户现在都有独立的目录"
echo "✅ 配置文件统一命名为 config.json"
echo "✅ 会话文件统一命名为 session.json"
echo "✅ 原始数据已备份到 users_backup/"
echo ""
echo "🚀 现在你可以使用新的目录结构了！"
echo "   - 查看用户: ls users/"
echo "   - 检查配置: cat users/用户名/config.json"
echo "   - 检查会话: ls users/用户名/session.json"
