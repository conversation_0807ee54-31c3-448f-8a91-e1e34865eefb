#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

function readJSON(filePath) {
  const raw = fs.readFileSync(filePath, 'utf8');
  return JSON.parse(raw);
}

function writeJSON(filePath, data) {
  fs.writeFileSync(filePath, JSON.stringify(data, null, 4) + '\n', 'utf8');
}

function ensureDir(dirPath) {
  fs.mkdirSync(dirPath, { recursive: true });
}

function splitNameCN(fullName) {
  if (!fullName) return { firstName: '', lastName: '' };
  const name = fullName.replace(/\s+/g, '');
  // If contains ASCII space separated western-style name, fallback to split by space
  const western = fullName.trim().split(/\s+/);
  const hasChinese = /[\u4e00-\u9fff]/.test(name);
  if (!hasChinese && western.length >= 2) {
    // Assume first token is firstName, last token is lastName
    return { firstName: western[0], lastName: western.slice(1).join(' ') };
  }
  if (name.length >= 2) {
    return { lastName: name[0], firstName: name.slice(1) };
  }
  return { firstName: name, lastName: '' };
}

function parseBlocks(text) {
  // Split by two or more newlines
  const blocks = text
    .split(/\n\s*\n+/)
    .map((b) => b.trim())
    .filter(Boolean);
  return blocks;
}

function extractField(regex, text) {
  const m = text.match(regex);
  return m ? m[1].trim() : undefined;
}

function extractEmails(text) {
  const emails = new Set();
  const re = /[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}/g;
  let m;
  while ((m = re.exec(text))) {
    emails.add(m[0]);
  }
  return [...emails];
}

function guessNameFromBlock(text) {
  // Try labeled name first
  let name = extractField(/(?:姓名|名字)[：:]?\s*([^\n]+)/, text);
  if (name) return name;
  // Otherwise take the first non-empty line that isn't a label and likely a name (2-4 Chinese chars)
  const firstLine = (text.split(/\n/).map((l) => l.trim()).find((l) => l && !/[:：]/.test(l))) || '';
  if (/[\u4e00-\u9fff]{2,4}/.test(firstLine)) return firstLine;
  return undefined;
}

function looksLikeEmail(s) {
  return /@/.test(s || '');
}

function buildConfigFromTemplate(tpl, { username, email, name, idLast4, phone, password }) {
  const cfg = JSON.parse(JSON.stringify(tpl));
  const userIdent = username || email;
  if (userIdent) {
    cfg.username = userIdent;
    cfg.sessionFileName = `${userIdent}_session.json`;
  }
  if (password) cfg.password = password;

  if (!cfg.userConfig) cfg.userConfig = {};
  // Prefer explicit email for contact; fallback to username if it looks like an email
  cfg.userConfig.email = email || (looksLikeEmail(username) ? username : cfg.userConfig.email);
  if (idLast4) cfg.userConfig.idCardLast4 = idLast4;
  if (name) {
    cfg.userConfig.idCardName = name;
    const { firstName, lastName } = splitNameCN(name);
    if (firstName) cfg.userConfig.firstName = firstName;
    if (lastName) cfg.userConfig.lastName = lastName;
  }
  if (phone) cfg.userConfig.phone = phone;
  return cfg;
}

function main() {
  const cwd = process.cwd();
  const accPath = path.join(cwd, 'accoutdata.txt');
  const tplPath = path.join(cwd, 'config.json.example.json');
  const usersRoot = path.join(cwd, 'users');

  if (!fs.existsSync(accPath)) {
    console.error(`Missing accoutdata.txt at ${accPath}`);
    process.exit(1);
  }
  if (!fs.existsSync(tplPath)) {
    console.error(`Missing template config at ${tplPath}`);
    process.exit(1);
  }

  const tpl = readJSON(tplPath);
  const accText = fs.readFileSync(accPath, 'utf8');

  const blocks = parseBlocks(accText);
  const generated = [];

  for (const block of blocks) {
    const emails = extractEmails(block);
    if (emails.length === 0) continue;

    const idLast4 = extractField(/身份证后四位[：:]?\s*([0-9Xx]{3,4})/, block) || extractField(/身份证后四位\s*([^\n\s]+)/, block);
    const name = guessNameFromBlock(block);
    const phone = extractField(/(?:手机号|手机)[：:]?\s*([0-9\-]+)/, block);
    const password = extractField(/密码[：:]?\s*([^\s\n]+)/, block);
    const username = extractField(/username[：:]?\s*([^\s\n]+)/i, block);

    // If username exists, generate one config using that as the directory key
    if (username) {
      const primaryEmail = emails[0];
      const cfg = buildConfigFromTemplate(tpl, { username, email: primaryEmail, name, idLast4, phone, password });
      const dir = path.join(usersRoot, username);
      ensureDir(dir);
      const outPath = path.join(dir, 'config.json');
      writeJSON(outPath, cfg);
      generated.push(outPath);
      continue;
    }

    // Fallback: generate for each email (legacy behavior)
    for (const email of emails) {
      const cfg = buildConfigFromTemplate(tpl, { email, name, idLast4, phone, password });
      const dir = path.join(usersRoot, email);
      ensureDir(dir);
      const outPath = path.join(dir, 'config.json');
      writeJSON(outPath, cfg);
      generated.push(outPath);
    }
  }

  if (generated.length === 0) {
    console.error('No configs generated. Check accoutdata.txt format.');
    process.exit(2);
  }

  console.log('Generated configs:');
  for (const g of generated) console.log(g);
}

if (require.main === module) {
  try {
    main();
  } catch (e) {
    console.error(e);
    process.exit(1);
  }
}
