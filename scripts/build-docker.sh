#!/bin/bash

# Apple Crawler Docker 构建脚本
# 支持不同的构建策略

set -e

echo "🚀 Apple Crawler Docker 构建脚本"

# 默认使用 Playwright 基础镜像 + GraalVM（推荐）
DOCKERFILE="Dockerfile.playwright-base"
BUILD_MODE="playwright"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --playwright)
            DOCKERFILE="Dockerfile.playwright-base"
            BUILD_MODE="playwright"
            shift
            ;;
        --graalvm)
            DOCKERFILE="Dockerfile"
            BUILD_MODE="graalvm"
            shift
            ;;
        --help)
            echo "使用方法: $0 [选项]"
            echo "选项:"
            echo "  --playwright    使用 Playwright 基础镜像 + 手动安装 GraalVM（默认，推荐）"
            echo "  --graalvm       使用 GraalVM 官方镜像 + 手动安装 Playwright"
            echo "  --help          显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo "📦 构建模式: $BUILD_MODE"
echo "📄 Dockerfile: $DOCKERFILE"

# 检查 Dockerfile 是否存在
if [ ! -f "$DOCKERFILE" ]; then
    echo "❌ Dockerfile 不存在: $DOCKERFILE"
    exit 1
fi

# 清理之前的构建
echo "🧹 清理之前的构建..."
docker-compose down 2>/dev/null || true
docker rmi applecrawler-applecrawler 2>/dev/null || true

# 构建镜像
echo "🏗️  开始构建 Docker 镜像..."
if [ "$BUILD_MODE" = "playwright" ]; then
    # 使用备用 Dockerfile
    docker-compose -f docker-compose.yml build --build-arg DOCKERFILE=$DOCKERFILE
else
    # 使用默认 Dockerfile
    docker-compose build
fi

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ Docker 镜像构建成功!"
    
    # 显示镜像信息
    echo "📊 镜像信息:"
    docker images | grep applecrawler
    
    echo ""
    echo "🚀 启动服务:"
    echo "  docker-compose up -d"
    echo ""
    echo "📋 查看日志:"
    echo "  docker-compose logs -f"
    echo ""
    echo "🛑 停止服务:"
    echo "  docker-compose down"
    
else
    echo "❌ Docker 镜像构建失败!"
    echo ""
    echo "💡 故障排除建议:"
    echo "  1. 检查网络连接"
    echo "  2. 尝试使用备用构建模式: $0 --playwright"
    echo "  3. 清理 Docker 缓存: docker system prune -a"
    exit 1
fi
