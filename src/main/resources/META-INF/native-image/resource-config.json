{"resources": {"includes": [{"pattern": "\\QMETA-INF/build-info.properties\\E"}, {"pattern": "\\QMETA-INF/services/ch.qos.logback.classic.spi.Configurator\\E"}, {"pattern": "\\QMETA-INF/services/java.lang.System$LoggerFinder\\E"}, {"pattern": "\\QMETA-INF/services/java.net.spi.URLStreamHandlerProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.channels.spi.SelectorProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.file.spi.FileSystemProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.time.zone.ZoneRulesProvider\\E"}, {"pattern": "\\QMETA-INF/services/org.slf4j.spi.SLF4JServiceProvider\\E"}, {"pattern": "\\QMETA-INF/spring-autoconfigure-metadata.properties\\E"}, {"pattern": "\\QMETA-INF/spring.components\\E"}, {"pattern": "\\QMETA-INF/spring.factories\\E"}, {"pattern": "\\QMETA-INF/spring.integration.properties\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports\\E"}, {"pattern": "\\QMETA-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.replacements\\E"}, {"pattern": "\\Qapplication-default.properties\\E"}, {"pattern": "\\Qapplication-default.xml\\E"}, {"pattern": "\\Qapplication-default.yaml\\E"}, {"pattern": "\\Qapplication-default.yml\\E"}, {"pattern": "\\Qapplication.properties\\E"}, {"pattern": "\\Qapplication.xml\\E"}, {"pattern": "\\Qapplication.yaml\\E"}, {"pattern": "\\Qapplication.yml\\E"}, {"pattern": "\\Qbanner.txt\\E"}, {"pattern": "\\Qcom/example/demo/SwimloginCrawler2.class\\E"}, {"pattern": "\\Qcom/example/demo/\\E"}, {"pattern": "\\Qconfig/application-default.properties\\E"}, {"pattern": "\\Qconfig/application-default.xml\\E"}, {"pattern": "\\Qconfig/application-default.yaml\\E"}, {"pattern": "\\Qconfig/application-default.yml\\E"}, {"pattern": "\\Qconfig/application.properties\\E"}, {"pattern": "\\Qconfig/application.xml\\E"}, {"pattern": "\\Qconfig/application.yaml\\E"}, {"pattern": "\\Qconfig/application.yml\\E"}, {"pattern": "\\Qdriver/mac-arm64\\E"}, {"pattern": "\\Qembedding/embedding-model-dimensions.properties\\E"}, {"pattern": "\\Qgit.properties\\E"}, {"pattern": "\\Qlogback-spring.groovy\\E"}, {"pattern": "\\Qlogback-spring.xml\\E"}, {"pattern": "\\Qlogback-test-spring.groovy\\E"}, {"pattern": "\\Qlogback-test-spring.xml\\E"}, {"pattern": "\\Qlogback-test.groovy\\E"}, {"pattern": "\\Qlogback-test.scmo\\E"}, {"pattern": "\\Qlogback-test.xml\\E"}, {"pattern": "\\Qlogback.groovy\\E"}, {"pattern": "\\Qlogback.scmo\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "\\Qmessages.properties\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/client/autoconfigure/ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/client/autoconfigure/ChatClientAutoConfiguration$TracerPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/client/autoconfigure/ChatClientAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/memory/autoconfigure/ChatMemoryAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/observation/autoconfigure/ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/observation/autoconfigure/ChatObservationAutoConfiguration$TracerPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/chat/observation/autoconfigure/ChatObservationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/embedding/observation/autoconfigure/EmbeddingObservationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/image/observation/autoconfigure/ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/image/observation/autoconfigure/ImageObservationAutoConfiguration$TracerPresentObservationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/image/observation/autoconfigure/ImageObservationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiAudioSpeechAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiAudioTranscriptionAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiChatAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiEmbeddingAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiImageAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/openai/autoconfigure/OpenAiModerationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/model/tool/autoconfigure/ToolCallingAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/ai/retry/autoconfigure/SpringAiRetryAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/beans/factory/Aware.class\\E"}, {"pattern": "\\Qorg/springframework/beans/factory/BeanClassLoaderAware.class\\E"}, {"pattern": "\\Qorg/springframework/beans/factory/config/BeanFactoryPostProcessor.class\\E"}, {"pattern": "\\Qorg/springframework/boot/actuate/autoconfigure/observation$ObservationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/actuate/autoconfigure/observation/ObservationAutoConfiguration$class.class\\E"}, {"pattern": "\\Qorg/springframework/boot/actuate/autoconfigure/observation/ObservationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/actuate/autoconfigure/observation/ObservationAutoConfiguration/class.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AbstractDependsOnBeanFactoryPostProcessor.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AutoConfigurationImportSelector.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AutoConfigureAfter.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AutoConfigureBefore.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/AutoConfigureOrder.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/ImportAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/ImportAutoConfigurationImportSelector.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/admin/SpringApplicationAdminJmxAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/aop/AopAutoConfiguration$AspectJAutoProxyingConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/aop/AopAutoConfiguration$ClassProxyingConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/aop/AopAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/availability/ApplicationAvailabilityAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheConfigurationImportSelector.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheManagerEntityManagerFactoryDependsOnPostProcessor.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/CacheAutoConfiguration$CacheManagerValidator.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/CacheAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/GenericCacheConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/NoOpCacheConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/cache/SimpleCacheConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnBean.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnBooleanProperty.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnClass.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnMissingBean.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnMissingClass.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/condition/ConditionalOnProperty.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/context/ConfigurationPropertiesAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/context/LifecycleAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/context/MessageSourceAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/context/PropertyPlaceholderAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/gson/GsonAutoConfiguration$StandardGsonBuilderCustomizer.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/gson/GsonAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/ConditionalOnPreferredJsonMapper.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$GsonHttpMessageConverterConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$JacksonAndJsonbUnavailableCondition$JacksonAvailable.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$JacksonAndJsonbUnavailableCondition$JsonbPreferred.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$JacksonAndJsonbUnavailableCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$PreferGsonOrJacksonAndJsonbUnavailableCondition$GsonPreferred.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$PreferGsonOrJacksonAndJsonbUnavailableCondition$JacksonJsonbUnavailable.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration$PreferGsonOrJacksonAndJsonbUnavailableCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/GsonHttpMessageConvertersConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$HttpMessageConvertersAutoConfigurationRuntimeHints.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition$ReactiveWebApplication.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/HttpMessageConvertersAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration$MappingJackson2XmlHttpMessageConverterConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/JacksonHttpMessageConvertersConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/JsonbHttpMessageConvertersConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/client/HttpClientAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/client/NotReactiveWebApplicationCondition$ReactiveWebApplication.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/client/NotReactiveWebApplicationCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/client/reactive/ClientHttpConnectorAutoConfiguration$ReactorNetty.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/client/reactive/ClientHttpConnectorAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration$DefaultCodecsConfiguration$DefaultCodecCustomizer.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration$DefaultCodecsConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration$JacksonCodecConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/http/codec/CodecsAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration$GitResourceAvailableCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/info/ProjectInfoAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonAutoConfigurationRuntimeHints.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonMixinConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$JacksonObjectMapperConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration$ParameterNamesModuleConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jackson/JacksonAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/jmx/JmxAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/mail/MailSenderValidatorAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/orm/jpa/EntityManagerFactoryDependsOnPostProcessor.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/reactor/ReactorAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/sql/init/DataSourceInitializationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/sql/init/R2dbcInitializationConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration$SqlInitializationModeCondition$ModeIsNever.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration$SqlInitializationModeCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/sql/init/SqlInitializationAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/ssl/SslAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutionAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$AsyncConfigurerConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$BootstrapExecutorConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$OnExecutorCondition$ExecutorBeanCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$OnExecutorCondition$ModelCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$OnExecutorCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$TaskExecutorConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskSchedulingAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$TaskSchedulerConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/task/TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationCondition$ReactiveWebApplication.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition$NotReactiveWebApplication.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition$VirtualThreadsExecutorEnabled.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/RestClientAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/client/RestTemplateAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/embedded/EmbeddedWebServerFactoryCustomizerAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/HttpHandlerAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/ReactiveMultipartAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/ReactiveWebServerFactoryAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/WebFluxAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/WebSessionIdResolverAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/error/ErrorWebFluxAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/function/client/ClientHttpConnectorAutoConfiguration$ReactorNetty.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/function/client/ClientHttpConnectorAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/function/client/WebClientAutoConfiguration$WebClientCodecsConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/autoconfigure/web/reactive/function/client/WebClientAutoConfiguration.class\\E"}, {"pattern": "\\Qorg/springframework/boot/context/properties/EnableConfigurationProperties.class\\E"}, {"pattern": "\\Qorg/springframework/boot/context/properties/EnableConfigurationPropertiesRegistrar.class\\E"}, {"pattern": "\\Qorg/springframework/boot/sql/init/dependency/DatabaseInitializationDependencyConfigurer.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/Conditional.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/Configuration.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/DeferredImportSelector.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/Import.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/ImportBeanDefinitionRegistrar.class\\E"}, {"pattern": "\\Qorg/springframework/context/annotation/ImportRuntimeHints.class\\E"}, {"pattern": "\\Qorg/springframework/core/Ordered.class\\E"}, {"pattern": "\\Qorg/springframework/http/codec/CodecConfigurer.properties\\E"}, {"pattern": "\\Qspring.properties\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/nfc.nrm\\E"}]}, "bundles": []}