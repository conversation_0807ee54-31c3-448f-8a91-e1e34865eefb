[{"name": "[B"}, {"name": "[I"}, {"name": "[Lcom.fasterxml.jackson.databind.deser.BeanDeserializerModifier;"}, {"name": "[Lcom.fasterxml.jackson.databind.deser.Deserializers;"}, {"name": "[Lcom.fasterxml.jackson.databind.deser.KeyDeserializers;"}, {"name": "[Lcom.fasterxml.jackson.databind.deser.ValueInstantiators;"}, {"name": "[Lcom.fasterxml.jackson.databind.ser.BeanSerializerModifier;"}, {"name": "[Lcom.fasterxml.jackson.databind.ser.Serializers;"}, {"name": "[Lcom.microsoft.playwright.impl.SerializedValue$O;"}, {"name": "[Lcom.microsoft.playwright.impl.SerializedValue;"}, {"name": "[Lcom.microsoft.playwright.options.HttpHeader;"}, {"name": "[Ljava.lang.Class;"}, {"name": "[Ljava.lang.String;"}, {"name": "[Lorg.springframework.core.annotation.AnnotationAttributes;"}, {"name": "[Lorg.springframework.util.ConcurrentReferenceHashMap$Segment;"}, {"name": "[Z"}, {"name": "apple.security.AppleProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "boolean", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "from", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "of", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "valueOf", "parameterTypes": ["java.lang.Bo<PERSON>an"]}]}, {"name": "ch.qos.logback.classic.BasicConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.LoggerContext"}, {"name": "ch.qos.logback.classic.joran.SerializedModelConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.util.DefaultJoranConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "co.elastic.clients.elasticsearch.ElasticsearchClient"}, {"name": "co.elastic.clients.transport.ElasticsearchTransport"}, {"name": "com.couchbase.client.java.Bucket"}, {"name": "com.couchbase.client.java.Cluster"}, {"name": "com.datastax.oss.driver.api.core.CqlSession"}, {"name": "com.example.demo.DemoApplication", "allDeclaredFields": true, "allDeclaredClasses": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "main", "parameterTypes": ["java.lang.String[]"]}, {"name": "runner", "parameterTypes": ["com.example.demo.SwimloginCrawler2"]}, {"name": "setBeanFactory", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "com.example.demo.DemoApplication$$SpringCGLIB$$0", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}, {"name": "CGLIB$SET_THREAD_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"name": "com.example.demo.DemoApplication$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "com.example.demo.DemoApplication$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "com.example.demo.SwimloginCrawler2", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.fasterxml.jackson.annotation.JacksonAnnotation", "queryAllDeclaredMethods": true}, {"name": "com.fasterxml.jackson.annotation.JsonInclude", "queryAllDeclaredMethods": true}, {"name": "com.fasterxml.jackson.core.JsonGenerator"}, {"name": "com.fasterxml.jackson.core.ObjectCodec", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "getJsonFactory", "parameterTypes": []}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.ResolvedType"]}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.TypeReference"]}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "java.lang.Class"]}]}, {"name": "com.fasterxml.jackson.core.TreeCodec", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "com.fasterxml.jackson.core.Versioned", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "com.fasterxml.jackson.databind.Module", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "com.fasterxml.jackson.databind.ObjectMapper", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "createArrayNode", "parameterTypes": []}, {"name": "createObjectNode", "parameterTypes": []}, {"name": "getFactory", "parameterTypes": []}, {"name": "missingNode", "parameterTypes": []}, {"name": "nullNode", "parameterTypes": []}, {"name": "readTree", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser"]}, {"name": "readValue", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.ResolvedType"]}, {"name": "readValue", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.TypeReference"]}, {"name": "readValue", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "java.lang.Class"]}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.ResolvedType"]}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "com.fasterxml.jackson.core.type.TypeReference"]}, {"name": "readValues", "parameterTypes": ["com.fasterxml.jackson.core.JsonParser", "java.lang.Class"]}, {"name": "shutdown", "parameterTypes": []}, {"name": "treeAsTokens", "parameterTypes": ["com.fasterxml.jackson.core.TreeNode"]}, {"name": "treeToValue", "parameterTypes": ["com.fasterxml.jackson.core.TreeNode", "java.lang.Class"]}, {"name": "version", "parameterTypes": []}, {"name": "writeTree", "parameterTypes": ["com.fasterxml.jackson.core.JsonGenerator", "com.fasterxml.jackson.core.TreeNode"]}, {"name": "writeValue", "parameterTypes": ["com.fasterxml.jackson.core.JsonGenerator", "java.lang.Object"]}]}, {"name": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.fasterxml.jackson.databind.module.SimpleModule", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "com.fasterxml.jackson.dataformat.cbor.CBORFactory"}, {"name": "com.fasterxml.jackson.dataformat.smile.SmileFactory"}, {"name": "com.fasterxml.jackson.dataformat.xml.XmlMapper"}, {"name": "com.fasterxml.jackson.dataformat.yaml.YAMLFactory"}, {"name": "com.fasterxml.jackson.datatype.jdk8.Jdk8Module"}, {"name": "com.fasterxml.jackson.datatype.jsr310.JavaTimeModule", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.fasterxml.jackson.module.paramnames.ParameterNamesModule"}, {"name": "com.github.benmanes.caffeine.cache.Caffeine"}, {"name": "com.google.gson.Gson", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "com.google.gson.GsonBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "com.google.gson.Strictness"}, {"name": "com.google.protobuf.Message"}, {"name": "com.hazelcast.core.HazelcastInstance"}, {"name": "com.microsoft.playwright.APIRequestContext$DisposeOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Browser$NewContextOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.BrowserContext$CloseOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.BrowserType$LaunchOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Frame$ClickOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Frame$GetByRoleOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Frame$InnerHTMLOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Frame$NavigateOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Locator$ClickOptions", "fields": [{"name": "button"}, {"name": "clickCount"}, {"name": "delay"}, {"name": "force"}, {"name": "modifiers"}, {"name": "noWaitAfter"}, {"name": "position"}, {"name": "strict"}, {"name": "timeout"}, {"name": "trial"}]}, {"name": "com.microsoft.playwright.Locator$GetByRoleOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Locator$InnerHTMLOptions", "fields": [{"name": "strict"}, {"name": "timeout"}]}, {"name": "com.microsoft.playwright.Frame$FillOptions", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Page"}, {"name": "com.microsoft.playwright.Page$CloseOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.Page$GetByRoleOptions", "fields": [{"name": "checked"}, {"name": "disabled"}, {"name": "exact"}, {"name": "expanded"}, {"name": "includeHidden"}, {"name": "level"}, {"name": "name"}, {"name": "pressed"}, {"name": "selected"}]}, {"name": "com.microsoft.playwright.Page$ScreenshotOptions", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.BrowserContextImpl"}, {"name": "com.microsoft.playwright.impl.ChannelOwner"}, {"name": "com.microsoft.playwright.impl.Connection"}, {"name": "com.microsoft.playwright.impl.FrameImpl"}, {"name": "com.microsoft.playwright.impl.LoggingSupport"}, {"name": "com.microsoft.playwright.impl.Message", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.PageImpl"}, {"name": "com.microsoft.playwright.impl.PageImpl$WaitablePageClose"}, {"name": "com.microsoft.playwright.impl.SerializedError", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.SerializedError$Error", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.SerializedValue", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.SerializedValue$E", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.SerializedValue$O", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.SerializedValue$R", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.impl.WaitForEventLogger"}, {"name": "com.microsoft.playwright.impl.WaitableRace"}, {"name": "com.microsoft.playwright.impl.WaitableResult"}, {"name": "com.microsoft.playwright.impl.driver.jar.DriverJar", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.ClientCertificate", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.Clip", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.Geolocation", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.HarContentPolicy", "allDeclaredFields": true}, {"name": "com.microsoft.playwright.options.HarMode", "allDeclaredFields": true}, {"name": "com.microsoft.playwright.options.HttpCredentials", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.HttpHeader", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.Position", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.Proxy", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.RecordVideoSize", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.ScreenSize", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.Timing", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.microsoft.playwright.options.ViewportSize", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.mongodb.client.MongoClient"}, {"name": "com.mongodb.reactivestreams.client.MongoClient"}, {"name": "com.nimbusds.jose.jwk.source.JWKSource"}, {"name": "com.querydsl.core.Query"}, {"name": "com.rabbitmq.client.Channel"}, {"name": "com.rometools.rome.feed.WireFeed"}, {"name": "com.samskivert.mustache.Mustache"}, {"name": "com.sendgrid.SendGrid"}, {"name": "com.sun.crypto.provider.AESCipher$General", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ARCFOURCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ChaCha20Cipher$ChaCha20Poly1305", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESedeCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DHParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.GaloisCounterMode$AESGCM", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.TlsMasterSecretGenerator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.unboundid.ldap.listener.InMemoryDirectoryServer"}, {"name": "freemarker.template.Configuration"}, {"name": "graphql.GraphQL"}, {"name": "groovy.lang.MetaClass"}, {"name": "groovy.text.markup.MarkupTemplateEngine"}, {"name": "io.micrometer.core.instrument.MeterRegistry"}, {"name": "io.micrometer.tracing.Tracer"}, {"name": "io.netty.buffer.ByteBuf"}, {"name": "io.netty.buffer.PooledByteBufAllocator"}, {"name": "io.netty.util.NettyRuntime"}, {"name": "io.netty5.buffer.Buffer"}, {"name": "io.r2dbc.proxy.ProxyConnectionFactory"}, {"name": "io.r2dbc.spi.ConnectionFactory"}, {"name": "io.rsocket.RSocket"}, {"name": "io.rsocket.core.RSocketServer"}, {"name": "jakarta.activation.MimeType"}, {"name": "jakarta.annotation.ManagedBean"}, {"name": "jakarta.annotation.PostConstruct"}, {"name": "jakarta.annotation.PreDestroy"}, {"name": "jakarta.annotation.Resource"}, {"name": "jakarta.ejb.EJB"}, {"name": "jakarta.inject.Inject"}, {"name": "jakarta.inject.Named"}, {"name": "jakarta.inject.Provider"}, {"name": "jakarta.inject.Qualifier"}, {"name": "jakarta.jms.ConnectionFactory"}, {"name": "jakarta.jms.Message"}, {"name": "jakarta.json.bind.Jsonb"}, {"name": "jakarta.persistence.EntityManager"}, {"name": "jakarta.persistence.EntityManagerFactory"}, {"name": "jakarta.servlet.MultipartConfigElement"}, {"name": "jakarta.servlet.Servlet"}, {"name": "jakarta.servlet.ServletRegistration"}, {"name": "jakarta.servlet.ServletRequest"}, {"name": "jakarta.transaction.Transaction"}, {"name": "jakarta.transaction.TransactionManager"}, {"name": "jakarta.validation.Validator"}, {"name": "jakarta.validation.executable.ExecutableValidator"}, {"name": "jakarta.xml.bind.Binder"}, {"name": "java.io.Closeable", "queryAllPublicMethods": true}, {"name": "java.io.Console", "methods": [{"name": "isTerminal", "parameterTypes": []}]}, {"name": "java.io.Serializable", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "to<PERSON><PERSON>", "parameterTypes": []}]}, {"name": "java.lang.Class", "queryAllDeclaredMethods": true, "methods": [{"name": "getRecordComponents", "parameterTypes": []}, {"name": "isRecord", "parameterTypes": []}]}, {"name": "java.lang.ClassLoader", "methods": [{"name": "defineClass", "parameterTypes": ["java.lang.String", "byte[]", "int", "int", "java.security.ProtectionDomain"]}]}, {"name": "java.lang.Iterable", "queryAllPublicMethods": true}, {"name": "java.lang.Module", "queryAllDeclaredMethods": true}, {"name": "java.lang.Object", "queryAllDeclaredMethods": true}, {"name": "java.lang.Record", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "java.lang.String", "queryAllDeclaredMethods": true}, {"name": "java.lang.Thread"}, {"name": "java.lang.annotation.Documented", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Inherited", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Repeatable", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Retention", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Target", "queryAllDeclaredMethods": true}, {"name": "java.lang.constant.Constable", "queryAllPublicMethods": true}, {"name": "java.lang.invoke.TypeDescriptor$OfField", "queryAllPublicMethods": true}, {"name": "java.lang.reflect.AnnotatedElement", "queryAllPublicMethods": true}, {"name": "java.lang.reflect.GenericDeclaration", "queryAllPublicMethods": true}, {"name": "java.lang.reflect.ParameterizedType", "methods": [{"name": "getActualTypeArguments", "parameterTypes": []}, {"name": "getRawType", "parameterTypes": []}]}, {"name": "java.lang.reflect.RecordComponent", "methods": [{"name": "getName", "parameterTypes": []}, {"name": "getType", "parameterTypes": []}]}, {"name": "java.lang.reflect.Type", "queryAllPublicMethods": true}, {"name": "java.lang.reflect.WildcardType", "methods": [{"name": "getLowerBounds", "parameterTypes": []}, {"name": "getUpperBounds", "parameterTypes": []}]}, {"name": "java.net.http.HttpClient"}, {"name": "java.security.AlgorithmParametersSpi"}, {"name": "java.security.KeyStoreSpi"}, {"name": "java.security.SecureRandomParameters"}, {"name": "java.sql.Date"}, {"name": "java.util.concurrent.Executor"}, {"name": "java.util.concurrent.Executors$RunnableAdapter"}, {"name": "java.util.concurrent.ForkJoinTask", "fields": [{"name": "aux"}, {"name": "status"}]}, {"name": "java.util.concurrent.FutureTask"}, {"name": "java.util.concurrent.ThreadFactory", "queryAllPublicMethods": true}, {"name": "java.util.concurrent.ThreadPoolExecutor"}, {"name": "java.util.concurrent.ThreadPoolExecutor$Worker"}, {"name": "java.util.concurrent.atomic.AtomicBoolean", "fields": [{"name": "value"}]}, {"name": "java.util.concurrent.atomic.AtomicReference", "fields": [{"name": "value"}]}, {"name": "java.util.logging.LogManager"}, {"name": "javax.annotation.ManagedBean"}, {"name": "javax.annotation.PostConstruct"}, {"name": "javax.annotation.PreDestroy"}, {"name": "javax.annotation.Resource"}, {"name": "javax.cache.Caching"}, {"name": "javax.inject.Inject"}, {"name": "javax.inject.Named"}, {"name": "javax.inject.Qualifier"}, {"name": "javax.money.MonetaryAmount"}, {"name": "javax.naming.InitialContext"}, {"name": "javax.naming.ldap.LdapContext"}, {"name": "javax.security.auth.x500.X500Principal", "fields": [{"name": "thisX500Name"}], "methods": [{"name": "<init>", "parameterTypes": ["sun.security.x509.X500Name"]}]}, {"name": "javax.sql.DataSource"}, {"name": "jdk.crac.management.CRaCMXBean"}, {"name": "jdk.internal.loader.ClassLoaders$AppClassLoader", "methods": [{"name": "clearCache", "parameterTypes": []}]}, {"name": "jdk.internal.loader.ClassLoaders$PlatformClassLoader", "methods": [{"name": "clearCache", "parameterTypes": []}]}, {"name": "jdk.internal.misc.Unsafe"}, {"name": "kotlin.Metadata"}, {"name": "kotlinx.coroutines.reactor.MonoKt"}, {"name": "kotlinx.serialization.cbor.Cbor"}, {"name": "kotlinx.serialization.json.Json"}, {"name": "kotlinx.serialization.protobuf.ProtoBuf"}, {"name": "liquibase.change.DatabaseChange"}, {"name": "org.apache.hc.client5.http.classic.HttpClient"}, {"name": "org.apache.hc.client5.http.impl.async.CloseableHttpAsyncClient"}, {"name": "org.apache.hc.client5.http.impl.async.HttpAsyncClients"}, {"name": "org.apache.hc.client5.http.impl.classic.HttpClients"}, {"name": "org.apache.logging.log4j.core.impl.Log4jContextFactory"}, {"name": "org.apache.logging.log4j.spi.ExtendedLogger"}, {"name": "org.apache.logging.slf4j.SLF4JProvider"}, {"name": "org.apache.pulsar.client.api.PulsarClient"}, {"name": "org.aspectj.weaver.Advice"}, {"name": "org.cache2k.Cache2kBuilder"}, {"name": "org.crac.Core"}, {"name": "org.eclipse.core.runtime.FileLocator"}, {"name": "org.eclipse.jetty.client.HttpClient"}, {"name": "org.elasticsearch.client.RestClientBuilder"}, {"name": "org.flywaydb.core.Flyway"}, {"name": "org.glassfish.jersey.servlet.ServletContainer"}, {"name": "org.h2.server.web.JakartaWebServlet"}, {"name": "org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager"}, {"name": "org.jboss.logging.Logger"}, {"name": "org.jooq.DSLContext"}, {"name": "org.neo4j.driver.Driver"}, {"name": "org.quartz.Scheduler"}, {"name": "org.reactivestreams.Publisher"}, {"name": "org.slf4j.<PERSON>"}, {"name": "org.slf4j.bridge.SLF4JBridgeHandler"}, {"name": "org.slf4j.spi.LocationAwareLogger"}, {"name": "org.springframework.ai.chat.client.ChatClient"}, {"name": "org.springframework.ai.chat.memory.ChatMemory", "queryAllPublicMethods": true}, {"name": "org.springframework.ai.chat.memory.ChatMemoryRepository", "queryAllPublicMethods": true}, {"name": "org.springframework.ai.chat.memory.InMemoryChatMemoryRepository", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.chat.memory.MessageWindowChatMemory", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.chat.model.ChatModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}, {"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.chat.model.StreamingChatModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.embedding.AbstractEmbeddingModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.ai.embedding.EmbeddingModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.image.ImageModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.model.Model", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}, {"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.model.StreamingModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "chatClientBuilderConfigurer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderConfigurer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.memory.autoconfigure.ChatMemoryAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "chatMemory", "parameterTypes": ["org.springframework.ai.chat.memory.ChatMemoryRepository"]}, {"name": "chatMemoryRepository", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.embedding.observation.autoconfigure.EmbeddingObservationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.image.observation.autoconfigure.ImageObservationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiAudioSpeechModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties", "org.springframework.retry.support.RetryTemplate", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.web.client.ResponseErrorHandler"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiAudioTranscriptionModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties", "org.springframework.retry.support.RetryTemplate", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.web.client.ResponseErrorHandler"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiChatAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiChatModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.ai.model.tool.ToolCallingManager", "org.springframework.retry.support.RetryTemplate", "org.springframework.web.client.ResponseErrorHandler", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "getOptions", "parameterTypes": []}, {"name": "setCompletionsPath", "parameterTypes": ["java.lang.String"]}, {"name": "setOptions", "parameterTypes": ["org.springframework.ai.openai.OpenAiChatOptions"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiEmbeddingModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.retry.support.RetryTemplate", "org.springframework.web.client.ResponseErrorHandler", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiImageModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.retry.support.RetryTemplate", "org.springframework.web.client.ResponseErrorHandler", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiModerationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "openAiModerationModel", "parameterTypes": ["org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties", "org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties", "org.springframework.retry.support.RetryTemplate", "org.springframework.beans.factory.ObjectProvider", "org.springframework.web.client.ResponseErrorHandler"]}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.openai.autoconfigure.OpenAiParentProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.String"]}, {"name": "setBaseUrl", "parameterTypes": ["java.lang.String"]}]}, {"name": "org.springframework.ai.model.tool.DefaultToolCallingManager", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.tool.ToolCallingChatOptions", "queryAllPublicMethods": true}, {"name": "org.springframework.ai.model.tool.ToolCallingManager", "queryAllPublicMethods": true}, {"name": "org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "toolCallbackResolver", "parameterTypes": ["org.springframework.context.support.GenericApplicationContext", "java.util.List", "java.util.List"]}, {"name": "toolCallingManager", "parameterTypes": ["org.springframework.ai.tool.resolution.ToolCallbackResolver", "org.springframework.ai.tool.execution.ToolExecutionExceptionProcessor", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "toolExecutionExceptionProcessor", "parameterTypes": []}]}, {"name": "org.springframework.ai.model.tool.autoconfigure.ToolCallingProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.moderation.ModerationModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.openai.OpenAiAudioSpeechModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.OpenAiAudioTranscriptionModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.OpenAiChatModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.OpenAiChatOptions", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "setModel", "parameterTypes": ["java.lang.String"]}]}, {"name": "org.springframework.ai.openai.OpenAiEmbeddingModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.OpenAiImageModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.OpenAiModerationModel", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.openai.api.OpenAiApi"}, {"name": "org.springframework.ai.openai.audio.speech.SpeechModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "call", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}, {"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.openai.audio.speech.StreamingSpeechModel", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "stream", "parameterTypes": ["org.springframework.ai.model.ModelRequest"]}]}, {"name": "org.springframework.ai.retry.RetryUtils"}, {"name": "org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "responseError<PERSON><PERSON>ler", "parameterTypes": ["org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties"]}, {"name": "retryTemplate", "parameterTypes": ["org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties"]}]}, {"name": "org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration$2", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.ai.tool.execution.DefaultToolExecutionExceptionProcessor", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.tool.execution.ToolExecutionExceptionProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.ai.tool.resolution.DelegatingToolCallbackResolver", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.ai.tool.resolution.ToolCallbackResolver", "queryAllPublicMethods": true}, {"name": "org.springframework.aop.framework.AopInfrastructureBean", "queryAllPublicMethods": true}, {"name": "org.springframework.aop.framework.ProxyConfig", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "setProxyTargetClass", "parameterTypes": ["boolean"]}]}, {"name": "org.springframework.aop.framework.ProxyProcessorSupport", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "setOrder", "parameterTypes": ["int"]}]}, {"name": "org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.aop.framework.autoproxy.InfrastructureAdvisorAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.aot.generate.Generated", "queryAllDeclaredMethods": true}, {"name": "org.springframework.batch.core.launch.JobLauncher"}, {"name": "org.springframework.beans.factory.Aware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanClassLoaderAware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanFactoryAware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanNameAware", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.DisposableBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.FactoryBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.InitializingBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.beans.factory.annotation.Value", "queryAllDeclaredMethods": true}, {"name": "org.springframework.beans.factory.aot.BeanFactoryInitializationAotProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.aot.BeanRegistrationAotProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.BeanFactoryPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.BeanPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.SmartInstantiationAwareBeanPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.ApplicationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.ClearCachesApplicationListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.CommandLineRunner", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.boot.Runner", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.boot.SpringBootConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.ansi.AnsiOutput$Enabled", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector$AutoConfigurationGroup", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackage", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$BasePackages", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$Registrar", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureAfter", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureBefore", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureOrder", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.BackgroundPreinitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.EnableAutoConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.ImportAutoConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.ImportAutoConfigurationImportSelector", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.SharedMetadataReaderFactoryContextInitializer", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.SpringBootApplication", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "forceAutoProxyCreatorToUseClassProxying", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationAvailability", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration$CacheConfigurationImportSelector", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheType", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.codec.CodecProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionEvaluationReportAutoConfigurationImportListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnBean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnBooleanProperty", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnClass", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnNotWarDeployment", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnProperty", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnResource", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnThreading", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication$Type"}, {"name": "org.springframework.boot.autoconfigure.condition.OnBeanCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnClassCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnPropertyCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnResourceCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnThreadingCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnWarDeploymentCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnWebApplicationCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.SearchStrategy"}, {"name": "org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "defaultLifecycleProcessor", "parameterTypes": ["org.springframework.boot.autoconfigure.context.LifecycleProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.context.LifecycleProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration$ResourceBundleCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "propertySourcesPlaceholderConfigurer", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializerDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "gson", "parameterTypes": ["com.google.gson.GsonBuilder"]}, {"name": "gsonBuilder", "parameterTypes": ["java.util.List"]}, {"name": "standardGsonBuilderCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.gson.GsonProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration$StandardGsonBuilderCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonBuilderCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.gson.GsonProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.ConditionalOnPreferredJsonMapper", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.http.ConditionalOnPreferredJsonMapper$JsonMapper"}, {"name": "org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration$JacksonAndJsonbUnavailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration$PreferGsonOrJacksonAndJsonbUnavailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.HttpMessageConverters", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "messageConverters", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$HttpMessageConvertersAutoConfigurationRuntimeHints"}, {"name": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "stringHttpMessageConverter", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"name": "org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "mappingJackson2HttpMessageConverter", "parameterTypes": ["com.fasterxml.jackson.databind.ObjectMapper"]}]}, {"name": "org.springframework.boot.autoconfigure.http.JsonbHttpMessageConvertersConfiguration"}, {"name": "org.springframework.boot.autoconfigure.http.OnPreferredJsonMapperCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.client.AbstractHttpClientProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.http.client.AbstractHttpRequestFactoryProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.autoconfigure.http.client.HttpClientProperties"]}, {"name": "clientHttpRequestFactoryBuilder", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "clientHttpRequestFactorySettings", "parameterTypes": []}, {"name": "set<PERSON>ean<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.ClassLoader"]}]}, {"name": "org.springframework.boot.autoconfigure.http.client.HttpClientProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.client.NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.client.reactive.AbstractClientHttpConnectorProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.http.client.reactive.ClientHttpConnectorAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.autoconfigure.http.client.reactive.HttpReactiveClientProperties"]}, {"name": "clientHttpConnector", "parameterTypes": ["org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder", "org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings"]}, {"name": "clientHttpConnectorBuilder", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "clientHttpConnectorSettings", "parameterTypes": []}, {"name": "set<PERSON>ean<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.ClassLoader"]}]}, {"name": "org.springframework.boot.autoconfigure.http.client.reactive.HttpReactiveClientProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "defaultCodecCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.codec.CodecProperties", "org.springframework.boot.autoconfigure.http.codec.HttpCodecsProperties", "org.springframework.core.env.Environment"]}]}, {"name": "org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration$DefaultCodecCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jacksonCodecCustomizer", "parameterTypes": ["com.fasterxml.jackson.databind.ObjectMapper"]}]}, {"name": "org.springframework.boot.autoconfigure.http.codec.HttpCodecsProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.info.ProjectInfoProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$GitResourceAvailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.integration.IntegrationPropertiesEnvironmentPostProcessor", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jsonComponentModule", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "standardJacksonObjectMapperBuilderCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.jackson.JacksonProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jsonMixinModule", "parameterTypes": ["org.springframework.context.ApplicationContext", "org.springframework.boot.jackson.JsonMixinModuleEntries"]}, {"name": "jsonMixinModuleEntries", "parameterTypes": ["org.springframework.context.ApplicationContext"]}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jacksonObjectMapperBuilder", "parameterTypes": ["org.springframework.context.ApplicationContext", "java.util.List"]}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "jacksonObjectMapper", "parameterTypes": ["org.springframework.http.converter.json.Jackson2ObjectMapperBuilder"]}]}, {"name": "org.springframework.boot.autoconfigure.jackson.JacksonProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration"}, {"name": "org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.reactor.ReactorProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.reactor.ReactorProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration"}, {"name": "org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration"}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration$SqlInitializationModeCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.ssl.FileWatcher", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader", "org.springframework.boot.autoconfigure.ssl.SslProperties"]}, {"name": "fileWatcher", "parameterTypes": []}, {"name": "sslBundleRegistry", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "sslPropertiesSslBundleRegistrar", "parameterTypes": ["org.springframework.boot.autoconfigure.ssl.FileWatcher"]}]}, {"name": "org.springframework.boot.autoconfigure.ssl.SslBundleRegistrar", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.ssl.SslProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.ssl.SslPropertiesBundleRegistrar", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$AsyncConfigurerConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationTaskExecutorAsyncConfigurer", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$AsyncConfigurerConfiguration$1", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$BootstrapExecutorConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "bootstrapExecutorAliasPostProcessor", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$OnExecutorCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskExecutorBuilder", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationTaskExecutor", "parameterTypes": ["org.springframework.boot.task.ThreadPoolTaskExecutorBuilder"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskExecutorBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskSchedulerBuilder", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerConfiguration"}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskSchedulerBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.thread.Threading"}, {"name": "org.springframework.boot.autoconfigure.web.client.AutoConfiguredRestClientSsl", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.client.HttpMessageConvertersRestClientCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.client.NotReactiveWebApplicationCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.client.NotReactiveWebApplicationOrVirtualThreadsExecutorEnabledCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "httpMessageConvertersRestClientCustomizer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "restClientBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer"]}, {"name": "restClientBuilderConfigurer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "restClientSsl", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.boot.ssl.SslBundles"]}]}, {"name": "org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.client.RestClientSsl", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.AutoConfiguredWebClientSsl", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "webClientBuilder", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "webClientHttpConnectorCustomizer", "parameterTypes": ["org.springframework.http.client.reactive.ClientHttpConnector"]}, {"name": "webClientSsl", "parameterTypes": ["org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder", "org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings", "org.springframework.boot.ssl.SslBundles"]}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration$WebClientCodecsConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "exchangeStrategiesCustomizer", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientCodecCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientSsl", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.availability.ApplicationAvailability", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.availability.ApplicationAvailabilityBean", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.builder.ParentContextCloserApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.cloud.CloudFoundryVcapEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"name": "org.springframework.boot.context.ConfigurationWarningsApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.ContextIdApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.FileEncodingApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.TypeExcludeFilter", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.AnsiOutputApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.ConfigurableBootstrapContext"]}]}, {"name": "org.springframework.boot.context.config.ConfigDataNotFoundAction", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigDataProperties", "queryAllDeclaredConstructors": true, "fields": [{"name": "this$0"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigTreeConfigDataLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigTreeConfigDataLocationResolver", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader"]}]}, {"name": "org.springframework.boot.context.config.StandardConfigDataLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.StandardConfigDataLocationResolver", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.context.properties.bind.Binder", "org.springframework.core.io.ResourceLoader"]}]}, {"name": "org.springframework.boot.context.config.SystemEnvironmentConfigDataLoader", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.SystemEnvironmentConfigDataLocationResolver", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.event.EventPublishingRunListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.SpringApplication", "java.lang.String[]"]}]}, {"name": "org.springframework.boot.context.logging.LoggingApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.BoundConfigurationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.ConfigurationProperties", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBinder$ConfigurationPropertiesBinderFactory", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.DeprecatedConfigurationProperty", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.EnableConfigurationProperties", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.NestedConfigurationProperty", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.bind.Name", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.bind.Nested", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.env.EnvironmentPostProcessorApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.PropertiesPropertySourceLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.RandomValuePropertySourceEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"name": "org.springframework.boot.env.SpringApplicationJsonEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.YamlPropertySourceLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.flyway.FlywayDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.http.client.AbstractClientHttpRequestFactoryBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.http.client.ClientHttpRequestFactorySettings", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.http.client.JdkClientHttpRequestFactoryBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.http.client.reactive.AbstractClientHttpConnectorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.http.client.reactive.ClientHttpConnectorBuilder", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.http.client.reactive.ClientHttpConnectorSettings", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.http.client.reactive.JdkClientHttpConnectorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.io.Base64ProtocolResolver", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.io.ClassPathResourceFilePathResolver", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.io.ProtocolResolverApplicationContextInitializer", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.jackson.JsonComponentModule", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.jackson.JsonMixinModule", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.jackson.JsonMixinModuleEntries", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.liquibase.LiquibaseDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.loader.launch.LaunchedClassLoader", "methods": [{"name": "clearCache", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.java.JavaLoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.logback.LogbackLoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.orm.jpa.JpaDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"name": "org.springframework.boot.r2dbc.init.R2dbcScriptDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.reactor.ReactorEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.rsocket.context.RSocketPortInfoApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.ssl.DefaultSslBundleRegistry", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.ssl.SslBundleRegistry", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.ssl.SslBundles", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.ThreadPoolTaskExecutorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory", "methods": [{"name": "toMetadataReaderFactory", "parameterTypes": []}]}, {"name": "org.springframework.boot.validation.beanvalidation.MethodValidationExcludeFilter", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "byAnnotation", "parameterTypes": ["java.lang.Class"]}]}, {"name": "org.springframework.boot.web.client.RestClientCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.web.codec.CodecCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.web.context.ServerPortInfoApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.reactive.context.FilteredReactiveWebContextResourceFilePathResolver", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContextFactory", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.reactive.function.client.WebClientCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.web.servlet.context.ServletContextResourceFilePathResolver", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.servlet.context.ServletWebServerApplicationContextFactory", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.servlet.server.Encoding", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.cache.Cache"}, {"name": "org.springframework.cache.CacheManager"}, {"name": "org.springframework.cache.interceptor.CacheAspectSupport"}, {"name": "org.springframework.context.ApplicationContextAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.ApplicationListener", "queryAllPublicMethods": true}, {"name": "org.springframework.context.ApplicationStartupAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.EnvironmentAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.LifecycleProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.context.ResourceLoaderAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.SmartLifecycle", "queryAllPublicMethods": true}, {"name": "org.springframework.context.annotation.AnnotationScopeMetadataResolver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.annotation.Bean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.CommonAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.annotation.ComponentScan", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ComponentScan$Filter", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Conditional", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Configuration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ConfigurationClassEnhancer$EnhancedConfiguration", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.context.annotation.ConfigurationClassPostProcessor", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMetadataReaderFactory", "parameterTypes": ["org.springframework.core.type.classreading.MetadataReaderFactory"]}]}, {"name": "org.springframework.context.annotation.Import", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ImportRuntimeHints", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Lazy", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Primary", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Scope", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.event.DefaultEventListenerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.event.EventListenerMethodProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.support.DefaultLifecycleProcessor", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.context.support.PropertySourcesPlaceholderConfigurer", "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.core.Ordered", "queryAllPublicMethods": true}, {"name": "org.springframework.core.PriorityOrdered", "queryAllPublicMethods": true}, {"name": "org.springframework.core.annotation.AliasFor", "queryAllDeclaredMethods": true}, {"name": "org.springframework.core.annotation.Order", "queryAllDeclaredMethods": true}, {"name": "org.springframework.core.task.AsyncListenableTaskExecutor", "queryAllPublicMethods": true}, {"name": "org.springframework.core.type.classreading.MetadataReaderFactory", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "from", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "of", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "valueOf", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}]}, {"name": "org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor"}, {"name": "org.springframework.data.cassandra.ReactiveSession"}, {"name": "org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate"}, {"name": "org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient"}, {"name": "org.springframework.data.elasticsearch.repository.ElasticsearchRepository"}, {"name": "org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration"}, {"name": "org.springframework.data.jpa.repository.JpaRepository"}, {"name": "org.springframework.data.ldap.repository.LdapRepository"}, {"name": "org.springframework.data.r2dbc.core.R2dbcEntityTemplate"}, {"name": "org.springframework.data.redis.connection.ReactiveRedisConnectionFactory"}, {"name": "org.springframework.data.redis.connection.RedisConnectionFactory"}, {"name": "org.springframework.data.redis.core.RedisOperations"}, {"name": "org.springframework.data.redis.repository.configuration.EnableRedisRepositories"}, {"name": "org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter"}, {"name": "org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration"}, {"name": "org.springframework.data.web.PageableHandlerMethodArgumentResolver"}, {"name": "org.springframework.hateoas.EntityModel"}, {"name": "org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter"}, {"name": "org.springframework.http.ReactiveHttpInputMessage"}, {"name": "org.springframework.http.client.ClientHttpRequestFactory"}, {"name": "org.springframework.http.client.reactive.ClientHttpConnector", "queryAllPublicMethods": true}, {"name": "org.springframework.http.client.reactive.JdkClientHttpConnector", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.http.codec.ClientCodecConfigurer"}, {"name": "org.springframework.http.codec.CodecConfigurer"}, {"name": "org.springframework.http.codec.ServerCodecConfigurer"}, {"name": "org.springframework.http.codec.multipart.DefaultPartHttpMessageReader"}, {"name": "org.springframework.http.codec.support.DefaultClientCodecConfigurer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.http.codec.support.DefaultServerCodecConfigurer"}, {"name": "org.springframework.http.converter.AbstractGenericHttpMessageConverter", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.http.converter.AbstractHttpMessageConverter", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "addDefaultHeaders", "parameterTypes": ["org.springframework.http.HttpHeaders", "java.lang.Object", "org.springframework.http.MediaType"]}, {"name": "getContentLength", "parameterTypes": ["java.lang.Object", "org.springframework.http.MediaType"]}, {"name": "supportsRepeatableWrites", "parameterTypes": ["java.lang.Object"]}, {"name": "writeInternal", "parameterTypes": ["java.lang.Object", "org.springframework.http.HttpOutputMessage"]}]}, {"name": "org.springframework.http.converter.GenericHttpMessageConverter", "queryAllPublicMethods": true}, {"name": "org.springframework.http.converter.HttpMessageConverter", "queryAllPublicMethods": true}, {"name": "org.springframework.http.converter.StringHttpMessageConverter", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.http.converter.json.GsonHttpMessageConverter"}, {"name": "org.springframework.http.converter.json.Jackson2ObjectMapperBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.http.converter.json.MappingJackson2HttpMessageConverter", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.http.server.reactive.HttpHandler"}, {"name": "org.springframework.integration.config.EnableIntegration"}, {"name": "org.springframework.jdbc.core.JdbcTemplate"}, {"name": "org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate"}, {"name": "org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType"}, {"name": "org.springframework.jdbc.datasource.init.DatabasePopulator"}, {"name": "org.springframework.jms.core.JmsTemplate"}, {"name": "org.springframework.jmx.export.MBeanExporter"}, {"name": "org.springframework.kafka.core.KafkaTemplate"}, {"name": "org.springframework.ldap.core.ContextSource"}, {"name": "org.springframework.mail.javamail.JavaMailSenderImpl"}, {"name": "org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean"}, {"name": "org.springframework.oxm.Marshaller"}, {"name": "org.springframework.r2dbc.connection.R2dbcTransactionManager"}, {"name": "org.springframework.r2dbc.connection.init.DatabasePopulator"}, {"name": "org.springframework.retry.RetryOperations", "queryAllPublicMethods": true}, {"name": "org.springframework.retry.support.RetryTemplate", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.scheduling.SchedulingTaskExecutor", "queryAllPublicMethods": true}, {"name": "org.springframework.scheduling.annotation.AsyncConfigurer", "queryAllPublicMethods": true}, {"name": "org.springframework.scheduling.concurrent.CustomizableThreadFactory", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.scheduling.concurrent.ExecutorConfigurationSupport", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler"}, {"name": "org.springframework.security.authentication.AuthenticationManager"}, {"name": "org.springframework.security.authentication.DefaultAuthenticationEventPublisher"}, {"name": "org.springframework.security.authentication.ReactiveAuthenticationManager"}, {"name": "org.springframework.security.config.annotation.web.configuration.EnableWebSecurity"}, {"name": "org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity"}, {"name": "org.springframework.security.config.http.SessionCreationPolicy"}, {"name": "org.springframework.security.oauth2.client.registration.ClientRegistration"}, {"name": "org.springframework.security.oauth2.server.authorization.OAuth2Authorization"}, {"name": "org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken"}, {"name": "org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor"}, {"name": "org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository"}, {"name": "org.springframework.session.Session"}, {"name": "org.springframework.stereotype.Component", "queryAllDeclaredMethods": true}, {"name": "org.springframework.stereotype.Indexed", "queryAllDeclaredMethods": true}, {"name": "org.springframework.stereotype.Service", "queryAllDeclaredMethods": true}, {"name": "org.springframework.transaction.PlatformTransactionManager"}, {"name": "org.springframework.util.CustomizableThreadCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.web.client.DefaultRestClientBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.web.client.ResponseErrorHandler", "queryAllPublicMethods": true}, {"name": "org.springframework.web.client.RestClient"}, {"name": "org.springframework.web.client.RestClient$Builder", "queryAllPublicMethods": true}, {"name": "org.springframework.web.client.RestTemplate"}, {"name": "org.springframework.web.context.support.GenericWebApplicationContext"}, {"name": "org.springframework.web.context.support.ServletContextResource"}, {"name": "org.springframework.web.filter.CharacterEncodingFilter"}, {"name": "org.springframework.web.reactive.DispatcherHandler"}, {"name": "org.springframework.web.reactive.HandlerResult"}, {"name": "org.springframework.web.reactive.config.WebFluxConfigurer"}, {"name": "org.springframework.web.reactive.function.client.DefaultWebClientBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.web.reactive.function.client.WebClient"}, {"name": "org.springframework.web.reactive.function.client.WebClient$Builder", "queryAllPublicMethods": true}, {"name": "org.springframework.web.server.session.WebSessionManager"}, {"name": "org.springframework.web.servlet.DispatcherServlet"}, {"name": "org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport"}, {"name": "org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer"}, {"name": "org.springframework.ws.transport.http.MessageDispatcherServlet"}, {"name": "org.thymeleaf.spring6.SpringTemplateEngine"}, {"name": "reactor.core.publisher.Flux"}, {"name": "reactor.core.publisher.Hooks"}, {"name": "reactor.core.publisher.Mono"}, {"name": "reactor.core.publisher.MonoCacheTime", "fields": [{"name": "state"}]}, {"name": "reactor.netty.http.client.HttpClient"}, {"name": "reactor.netty5.http.client.HttpClient"}, {"name": "reactor.tools.agent.ReactorDebugAgent"}, {"name": "sun.misc.Unsafe", "fields": [{"name": "theUnsafe"}], "methods": [{"name": "allocateInstance", "parameterTypes": ["java.lang.Class"]}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore$DualFormatPKCS12", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSA$SHA224withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSA$SHA256withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.NativePRNG", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["java.security.SecureRandomParameters"]}]}, {"name": "sun.security.provider.SHA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA224", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA512", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.X509Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.PSSParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAKeyFactory$Legacy", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAPSSSignature", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA224withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.KeyManagerFactoryImpl$SunX509", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.SSLContextImpl$DefaultSSLContext", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.TrustManagerFactoryImpl$PKIXFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.x509.AuthorityInfoAccessExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.AuthorityKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.BasicConstraintsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CRLDistributionPointsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CertificatePoliciesExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.ExtendedKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.IssuerAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.KeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.NetscapeCertTypeExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.PrivateKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}]