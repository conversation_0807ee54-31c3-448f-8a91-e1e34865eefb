package com.crawler.apple.http;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.pool.*;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.*;
import io.netty.handler.codec.http.HttpContentDecompressor;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.util.CharsetUtil;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 使用 Netty 异步模型实现的库存查询，功能等价于 HttpStockCheckerFromFile。
 * - 复用全局 EventLoopGroup
 * - 针对相同 host:port 使用连接池减少握手
 * - 设置连接/读超时，避免阻塞 EventLoop
 */
public class NettyHttpStockCheckerFromFile {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String DEFAULT_SESSION_FILE = "/Users/<USER>/Desktop/applecrawer/apple-auth.json";
    private static final Path SESSION_FILE = Paths.get(System.getProperty("httpStockChecker.sessionFile", DEFAULT_SESSION_FILE));

    // 全局 EventLoopGroup（复用且在 JVM 退出时优雅关闭）
    private static final NioEventLoopGroup EVENT_LOOP_GROUP = new NioEventLoopGroup(Math.max(2, Runtime.getRuntime().availableProcessors()));

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                EVENT_LOOP_GROUP.shutdownGracefully(0, 3, TimeUnit.SECONDS).syncUninterruptibly();
            } catch (Exception ignored) {}
        }));
    }

    // 每个远端地址一个连接池
    private static final Map<InetSocketAddress, FixedChannelPool> POOLS = new java.util.concurrent.ConcurrentHashMap<>();

    public static void main(String[] args) throws Exception {
        StockResult res = checkStockAvailabilityAsync().get(20, TimeUnit.SECONDS);
        System.out.println("\n" + res);
    }

    public static CompletableFuture<StockResult> checkStockAvailabilityAsync() throws Exception {
        RequestParameters requestParameters = loadRequestParameters();
        URI uri = URI.create(requestParameters.getUrl());
        boolean ssl = "https".equalsIgnoreCase(uri.getScheme());
        int port = uri.getPort() > 0 ? uri.getPort() : (ssl ? 443 : 80);
        String host = Objects.requireNonNull(uri.getHost(), "URL 缺少 host");

        System.out.println("🔍 正在检查Apple库存 (Netty) ...");
        System.out.println("📂 会话文件: " + SESSION_FILE.toAbsolutePath());
        System.out.println("📍 请求URL: " + requestParameters.getUrl());
        Instant start = Instant.now();
        System.out.println("⏱️  请求开始时间: " + start);

        return performRequest(host, port, ssl, uri, requestParameters)
            .thenApply(payload -> {
                long durationMs = Duration.between(start, payload.endTime).toMillis();
                System.out.println("📊 响应状态码: " + payload.status);
                System.out.println("⏱️  请求耗时: " + durationMs + "ms");
                if (payload.status == 200) {
                    return parseStockResponse(payload.body, durationMs);
                } else {
                    return new StockResult(false, "请求失败", null, durationMs);
                }
            });
    }

    private static CompletableFuture<ResponsePayload> performRequest(String host, int port, boolean ssl, URI uri,
                                                                     RequestParameters params) throws SSLException {
        InetSocketAddress address = new InetSocketAddress(host, port);
        FixedChannelPool pool = POOLS.computeIfAbsent(address, key -> newPool(host, port, ssl));

        CompletableFuture<ResponsePayload> outer = new CompletableFuture<>();
        Future<Channel> acquireFuture = pool.acquire();

        acquireFuture.addListener(f -> {
            if (!f.isSuccess()) {
                outer.completeExceptionally(f.cause());
                return;
            }
            Channel ch = (Channel) acquireFuture.getNow();

            // 为本次请求挂一个临时的响应处理器
            Promise<ResponsePayload> promise = ch.eventLoop().newPromise();
            ChannelInboundHandler responseHandler = new SimpleChannelInboundHandler<FullHttpResponse>() {
                @Override
                protected void channelRead0(ChannelHandlerContext ctx, FullHttpResponse msg) {
                    ByteBuf content = msg.content();
                    String body = content.toString(CharsetUtil.UTF_8);
                    int status = msg.status().code();
                    promise.trySuccess(new ResponsePayload(status, body, Instant.now()));
                }

                @Override
                public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                    promise.tryFailure(cause);
                }

                @Override
                public void channelInactive(ChannelHandlerContext ctx) throws Exception {
                    super.channelInactive(ctx);
                }
            };

            ch.pipeline().addLast("respHandler-" + System.identityHashCode(responseHandler), responseHandler);

            // 发送 HTTP 请求（保持长连接以便复用）
            String path = uri.getRawPath() == null || uri.getRawPath().isEmpty() ? "/" : uri.getRawPath();
            if (uri.getRawQuery() != null && !uri.getRawQuery().isEmpty()) {
                path += "?" + uri.getRawQuery();
            }
            DefaultFullHttpRequest req = new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, HttpMethod.GET, path);
            HttpHeaders headers = req.headers();
            headers.set(HttpHeaderNames.HOST, host);
            headers.set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);

            // 复制用户自定义请求头（排除受管控的）
            for (Map.Entry<String, String> e : params.getHeaders().entrySet()) {
                headers.set(e.getKey(), e.getValue());
            }
            headers.set(HttpHeaderNames.COOKIE, params.getCookieHeader());
            headers.set(HttpHeaderNames.REFERER, params.getReferer());
            headers.set(HttpHeaderNames.USER_AGENT, params.getUserAgent());

            ChannelFuture writeFuture = ch.writeAndFlush(req);

            // 单次请求级别的超时（避免请求挂死）
            long requestTimeoutSec = 15;
            ch.eventLoop().schedule(() -> {
                if (!promise.isDone()) {
                    promise.tryFailure(new java.util.concurrent.TimeoutException("request timeout"));
                }
            }, requestTimeoutSec, TimeUnit.SECONDS);

            // 将 Netty Promise -> CompletableFuture，并负责释放连接
            promise.addListener(p -> {
                // 移除本次处理器，避免下次请求重复触发
                try {
                    ch.pipeline().remove(responseHandler);
                } catch (Exception ignored) {}

                if (p.isSuccess()) {
                    outer.complete((ResponsePayload) p.get());
                } else {
                    outer.completeExceptionally(p.cause());
                }

                // 释放连接回池
                pool.release(ch);
            });

            writeFuture.addListener(wf -> {
                if (!wf.isSuccess()) {
                    promise.tryFailure(wf.cause());
                }
            });
        });

        return outer;
    }

    private static FixedChannelPool newPool(String host, int port, boolean ssl) {
        try {
            final SslContext sslCtx = ssl ? SslContextBuilder.forClient().build() : null;

            Bootstrap bootstrap = new Bootstrap()
                .group(EVENT_LOOP_GROUP)
                .channel(NioSocketChannel.class)
                .remoteAddress(host, port)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10_000);

            ChannelPoolHandler poolHandler = new ChannelPoolHandler() {
                @Override
                public void channelReleased(Channel ch) { }

                @Override
                public void channelAcquired(Channel ch) { }

                @Override
                public void channelCreated(Channel ch) {
                    ChannelPipeline p = ch.pipeline();
                    if (sslCtx != null) {
                        p.addLast("ssl", sslCtx.newHandler(ch.alloc(), host, port));
                    }
                    p.addLast("codec", new HttpClientCodec());
                    p.addLast("decompressor", new HttpContentDecompressor());
                    p.addLast("aggregator", new HttpObjectAggregator(5 * 1024 * 1024));
                    p.addLast("readTimeout", new ReadTimeoutHandler(10)); // 读超时
                }
            };

            // 固定大小连接池，最大并发连接数可按需调整
            int maxConnections = 50;
            int maxPendingAcquires = 200;
            return new FixedChannelPool(
                bootstrap,
                poolHandler,
                ChannelHealthChecker.ACTIVE,
                FixedChannelPool.AcquireTimeoutAction.FAIL,
                5_000, // 获取连接超时
                maxConnections,
                maxPendingAcquires,
                true
            );
        } catch (SSLException e) {
            throw new RuntimeException(e);
        }
    }

    private static RequestParameters loadRequestParameters() throws IOException {
        if (!Files.exists(SESSION_FILE)) {
            createExampleAuthFile(SESSION_FILE);
            throw new IllegalStateException("会话文件不存在，已创建示例文件: " + SESSION_FILE.toAbsolutePath() +
                "\n请运行AppleLoginCrawler登录后重新尝试，或手动配置cookies。");
        }

        JsonNode rootNode;
        try (java.io.BufferedReader reader = Files.newBufferedReader(SESSION_FILE)) {
            rootNode = objectMapper.readTree(reader);
        } catch (IOException e) {
            throw new IOException("读取会话文件失败: " + SESSION_FILE.toAbsolutePath(), e);
        }

        JsonNode httpNode = rootNode.path("httpStockChecker");
        if (httpNode.isMissingNode() || !httpNode.isObject()) {
            if (addHttpStockCheckerConfig(SESSION_FILE)) {
                try (java.io.BufferedReader reader = Files.newBufferedReader(SESSION_FILE)) {
                    rootNode = objectMapper.readTree(reader);
                    httpNode = rootNode.path("httpStockChecker");
                }
            } else {
                throw new IllegalStateException("apple-auth.json 缺少 httpStockChecker 配置，且自动添加失败\n" +
                    "请运行AppleLoginCrawler重新登录，或手动添加httpStockChecker配置。");
            }
        }

        String url = requireText(httpNode, "url");
        String referer = requireText(httpNode, "referer");
        String userAgent = requireText(httpNode, "userAgent");
        Map<String, String> headers = extractHeaders(httpNode);
        String cookieHeader = buildCookieHeader(rootNode.path("cookies"));
        return new RequestParameters(url, referer, userAgent, headers, cookieHeader);
    }

    private static String requireText(JsonNode node, String fieldName) {
        JsonNode valueNode = node.get(fieldName);
        if (valueNode == null || !valueNode.isTextual()) {
            throw new IllegalStateException("httpStockChecker 缺少必填字段: " + fieldName);
        }
        return valueNode.asText();
    }

    private static Map<String, String> extractHeaders(JsonNode httpNode) {
        JsonNode headersNode = httpNode.get("headers");
        if (headersNode == null || !headersNode.isObject()) {
            throw new IllegalStateException("httpStockChecker.headers 必须存在且为对象");
        }

        Map<String, String> headers = new LinkedHashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = headersNode.fields();
        int headerCount = 0;

        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String headerName = entry.getKey();
            if (isManagedHeader(headerName)) {
                continue;
            }
            headers.put(headerName, entry.getValue().asText());
            headerCount++;
        }

        // 如果没有有效的headers，抛出错误提示用户通过AppleLoginCrawler获取
        if (headerCount == 0) {
            throw new IllegalStateException(
                "httpStockChecker.headers 为空，请运行 AppleLoginCrawler 登录以获取完整的 headers 配置\n" +
                "示例命令: java -jar apple-crawler.jar --login <username>"
            );
        }

        return headers;
    }

    private static boolean isManagedHeader(String headerName) {
        String normalized = headerName.toLowerCase();
        return "cookie".equals(normalized) || "referer".equals(normalized) || "user-agent".equals(normalized) || "host".equals(normalized) || "connection".equals(normalized);
    }

    private static String buildCookieHeader(JsonNode cookiesNode) {
        if (cookiesNode == null || !cookiesNode.isArray()) {
            throw new IllegalStateException("apple-auth.json 缺少 cookies 数组");
        }
        StringBuilder cookieHeader = new StringBuilder();
        for (JsonNode cookie : cookiesNode) {
            JsonNode nameNode = cookie.get("name");
            JsonNode valueNode = cookie.get("value");
            if (nameNode == null || valueNode == null) {
                continue;
            }
            if (cookieHeader.length() > 0) cookieHeader.append("; ");
            cookieHeader.append(nameNode.asText()).append("=").append(valueNode.asText());
        }
        if (cookieHeader.length() == 0) {
            throw new IllegalStateException("会话文件中未找到可用的Cookie");
        }
        return cookieHeader.toString();
    }

    private static StockResult parseStockResponse(String jsonResponse, long durationMs) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonResponse);
            if (!rootNode.has("body") || !rootNode.get("body").has("content")) {
                return new StockResult(false, "响应格式异常", null, durationMs);
            }
            JsonNode contentNode = rootNode.get("body").get("content");
            if (!contentNode.has("pickupMessage")) {
                return new StockResult(false, "无库存信息", null, durationMs);
            }
            JsonNode pickupMessage = contentNode.get("pickupMessage");
            if (!pickupMessage.has("stores")) {
                return new StockResult(false, "无门店信息", null, durationMs);
            }
            boolean hasStock = false;
            StringBuilder stockInfo = new StringBuilder();
            for (JsonNode store : pickupMessage.get("stores")) {
                String storeName = store.get("storeName").asText();
                String storeNumber = store.get("storeNumber").asText();
                boolean isAvailable = store.get("partsAvailability").get("MYEV3CH/A").get("pickupDisplay").asText().contains("available");
                stockInfo.append(String.format("🏪 %s (%s): %s%n", storeName, storeNumber, isAvailable ? "✅ 有货" : "❌ 无货"));
                if (isAvailable) hasStock = true;
            }
            return new StockResult(hasStock, stockInfo.toString().trim(), pickupMessage, durationMs);
        } catch (Exception e) {
            System.err.println("❌ 解析JSON响应失败: " + e.getMessage());
            return new StockResult(false, "解析失败: " + e.getMessage(), null, durationMs);
        }
    }

    // --- 数据结构与工具 ---

    private static final class RequestParameters {
        private final String url;
        private final String referer;
        private final String userAgent;
        private final Map<String, String> headers;
        private final String cookieHeader;

        private RequestParameters(String url, String referer, String userAgent, Map<String, String> headers, String cookieHeader) {
            this.url = url; this.referer = referer; this.userAgent = userAgent; this.headers = headers; this.cookieHeader = cookieHeader;
        }
        public String getUrl() { return url; }
        public String getReferer() { return referer; }
        public String getUserAgent() { return userAgent; }
        public Map<String, String> getHeaders() { return headers; }
        public String getCookieHeader() { return cookieHeader; }
    }

    private static final class ResponsePayload {
        final int status; final String body; final Instant endTime;
        ResponsePayload(int status, String body, Instant endTime) { this.status = status; this.body = body; this.endTime = endTime; }
    }

    public static class StockResult {
        private final boolean hasStock; private final String message; private final JsonNode rawData; private final long durationMs;
        public StockResult(boolean hasStock, String message, JsonNode rawData) { this(hasStock, message, rawData, -1); }
        public StockResult(boolean hasStock, String message, JsonNode rawData, long durationMs) { this.hasStock = hasStock; this.message = message; this.rawData = rawData; this.durationMs = durationMs; }
        public boolean hasStock() { return hasStock; }
        public String getMessage() { return message; }
        public JsonNode getRawData() { return rawData; }
        public long getDurationMs() { return durationMs; }
        @Override public String toString() {
            String durationInfo = durationMs > 0 ? String.format("⏱️  检查耗时: %dms%n", durationMs) : "";
            return String.format("库存状态: %s%n%s%s", hasStock ? "✅ 有货" : "❌ 无货", durationInfo, message);
        }
    }

    private static void createExampleAuthFile(Path authFile) {
        try {
            ObjectNode rootNode = objectMapper.createObjectNode();
            rootNode.set("cookies", objectMapper.createArrayNode());
            rootNode.set("origins", objectMapper.createArrayNode());

            // 创建基本的httpStockChecker配置结构（不包含具体的headers）
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            httpStockCheckerNode.put("url", "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=R670&little=false&parts.0=MYEV3CH/A&mts.0=regular&mts.1=sticky&fts=true");
            httpStockCheckerNode.put("referer", "https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            // headers将通过AppleLoginCrawler动态捕获和填充
            httpStockCheckerNode.set("headers", objectMapper.createObjectNode());

            rootNode.set("httpStockChecker", httpStockCheckerNode);

            if (authFile.getParent() != null) Files.createDirectories(authFile.getParent());
            Files.writeString(authFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
            System.out.println("✅ 已创建示例auth文件: " + authFile.toAbsolutePath());
            System.out.println("ℹ️  请运行 AppleLoginCrawler 登录以获取完整的 headers 配置");
        } catch (Exception e) {
            System.err.println("❌ 创建示例auth文件失败: " + e.getMessage());
        }
    }

    private static boolean addHttpStockCheckerConfig(Path authFile) {
        try {
            String content = Files.readString(authFile);
            JsonNode rootNode = objectMapper.readTree(content);
            if (!(rootNode instanceof ObjectNode)) return false;
            ObjectNode objectNode = (ObjectNode) rootNode;
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            httpStockCheckerNode.put("url", "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=R670&little=false&parts.0=MYEV3CH/A&mts.0=regular&mts.1=sticky&fts=true");
            httpStockCheckerNode.put("referer", "https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            // headers将通过AppleLoginCrawler动态捕获和填充
            httpStockCheckerNode.set("headers", objectMapper.createObjectNode());
            objectNode.set("httpStockChecker", httpStockCheckerNode);
            Files.writeString(authFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
            System.out.println("✅ 已自动添加httpStockChecker配置到: " + authFile.toAbsolutePath());
            System.out.println("ℹ️  请运行 AppleLoginCrawler 登录以获取完整的 headers 配置");
            return true;
        } catch (Exception e) {
            System.err.println("❌ 自动添加httpStockChecker配置失败: " + e.getMessage());
            return false;
        }
    }
}
