package com.crawler.apple.http;

import java.io.IOException;
import java.net.CookieManager;
import java.net.CookiePolicy;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.Instant;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class HttpStockCheckerFromFile {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String DEFAULT_SESSION_FILE = "/Users/<USER>/Desktop/applecrawer/apple-auth.json";
    private static final Path SESSION_FILE = Paths.get(
        System.getProperty("httpStockChecker.sessionFile", DEFAULT_SESSION_FILE)
    );

    public static void main(String[] args) throws Exception {
        StockResult result = checkStockAvailability();
        System.out.println("\n" + result);
    }

    public static StockResult checkStockAvailability() throws Exception {
        RequestParameters requestParameters = loadRequestParameters();

        // 1. 创建支持Cookie的HTTP客户端
        CookieManager cookieManager = new CookieManager();
        cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ALL);

        HttpClient client = HttpClient.newBuilder()
            .cookieHandler(cookieManager)
            .version(HttpClient.Version.HTTP_2)
            .connectTimeout(Duration.ofSeconds(10))
            .build();

        // 2. 构建HTTP请求，精确复刻资源快照中的所有请求头
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
            .uri(URI.create(requestParameters.getUrl()))
            .timeout(Duration.ofSeconds(15))
            .GET();

        requestParameters.getHeaders().forEach(requestBuilder::header);
        requestBuilder.header("Cookie", requestParameters.getCookieHeader());
        requestBuilder.header("Referer", requestParameters.getReferer());
        requestBuilder.header("User-Agent", requestParameters.getUserAgent());

        HttpRequest request = requestBuilder.build();

        // 3. 发送请求并获取响应
        System.out.println("🔍 正在检查Apple库存...");
        System.out.println("📂 会话文件: " + SESSION_FILE.toAbsolutePath());
        System.out.println("📍 请求URL: " + requestParameters.getUrl());

        // 记录请求开始时间
        Instant startTime = Instant.now();
        System.out.println("⏱️  请求开始时间: " + startTime);

        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        // 记录请求结束时间
        Instant endTime = Instant.now();
        long durationMs = java.time.Duration.between(startTime, endTime).toMillis();

        // 4. 分析响应
        System.out.println("📊 响应状态码: " + response.statusCode());
        System.out.println("⏱️  请求耗时: " + durationMs + "ms");

        if (response.statusCode() == 200) {
            return parseStockResponse(response.body(), durationMs);
        } else {
            System.err.println("❌ 请求失败，状态码: " + response.statusCode());
            System.err.println("响应内容: " + response.body());
            return new StockResult(false, "请求失败", null, durationMs);
        }
    }
    
    private static RequestParameters loadRequestParameters() throws IOException {
        if (!Files.exists(SESSION_FILE)) {
            // 如果文件不存在，创建示例文件
            createExampleAuthFile(SESSION_FILE);
            throw new IllegalStateException("会话文件不存在，已创建示例文件: " + SESSION_FILE.toAbsolutePath() +
                "\n请运行AppleLoginCrawler登录后重新尝试，或手动配置cookies。");
        }

        JsonNode rootNode;
        try (java.io.BufferedReader reader = Files.newBufferedReader(SESSION_FILE)) {
            rootNode = objectMapper.readTree(reader);
        } catch (IOException e) {
            throw new IOException("读取会话文件失败: " + SESSION_FILE.toAbsolutePath(), e);
        }

        JsonNode httpNode = rootNode.path("httpStockChecker");
        if (httpNode.isMissingNode() || !httpNode.isObject()) {
            // 如果缺少httpStockChecker配置，尝试自动添加
            if (addHttpStockCheckerConfig(SESSION_FILE)) {
                // 重新读取文件
                try (java.io.BufferedReader reader = Files.newBufferedReader(SESSION_FILE)) {
                    rootNode = objectMapper.readTree(reader);
                    httpNode = rootNode.path("httpStockChecker");
                }
            } else {
                throw new IllegalStateException("apple-auth.json 缺少 httpStockChecker 配置，且自动添加失败\n" +
                    "请运行AppleLoginCrawler重新登录，或手动添加httpStockChecker配置。");
            }
        }

        String url = requireText(httpNode, "url");
        String referer = requireText(httpNode, "referer");
        String userAgent = requireText(httpNode, "userAgent");
        Map<String, String> headers = extractHeaders(httpNode);
        String cookieHeader = buildCookieHeader(rootNode.path("cookies"));

        return new RequestParameters(url, referer, userAgent, headers, cookieHeader);
    }

    private static String requireText(JsonNode node, String fieldName) {
        JsonNode valueNode = node.get(fieldName);
        if (valueNode == null || !valueNode.isTextual()) {
            throw new IllegalStateException("httpStockChecker 缺少必填字段: " + fieldName);
        }
        return valueNode.asText();
    }

    private static Map<String, String> extractHeaders(JsonNode httpNode) {
        JsonNode headersNode = httpNode.get("headers");
        if (headersNode == null || !headersNode.isObject()) {
            throw new IllegalStateException("httpStockChecker.headers 必须存在且为对象");
        }

        Map<String, String> headers = new LinkedHashMap<>();
        Iterator<Map.Entry<String, JsonNode>> fields = headersNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String headerName = entry.getKey();
            if (isManagedHeader(headerName)) {
                continue;
            }
            headers.put(headerName, entry.getValue().asText());
        }
        return headers;
    }

    private static boolean isManagedHeader(String headerName) {
        String normalized = headerName.toLowerCase();
        return "cookie".equals(normalized)
            || "referer".equals(normalized)
            || "user-agent".equals(normalized);
    }

    private static String buildCookieHeader(JsonNode cookiesNode) {
        if (cookiesNode == null || !cookiesNode.isArray()) {
            throw new IllegalStateException("apple-auth.json 缺少 cookies 数组");
        }

        StringBuilder cookieHeader = new StringBuilder();
        for (JsonNode cookie : cookiesNode) {
            JsonNode nameNode = cookie.get("name");
            JsonNode valueNode = cookie.get("value");
            if (nameNode == null || valueNode == null) {
                continue;
            }

            if (cookieHeader.length() > 0) {
                cookieHeader.append("; ");
            }
            cookieHeader.append(nameNode.asText()).append("=").append(valueNode.asText());
        }

        if (cookieHeader.length() == 0) {
            throw new IllegalStateException("会话文件中未找到可用的Cookie");
        }

        return cookieHeader.toString();
    }

    private static final class RequestParameters {
        private final String url;
        private final String referer;
        private final String userAgent;
        private final Map<String, String> headers;
        private final String cookieHeader;

        private RequestParameters(String url, String referer, String userAgent,
                                  Map<String, String> headers, String cookieHeader) {
            this.url = url;
            this.referer = referer;
            this.userAgent = userAgent;
            this.headers = headers;
            this.cookieHeader = cookieHeader;
        }

        public String getUrl() {
            return url;
        }

        public String getReferer() {
            return referer;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public Map<String, String> getHeaders() {
            return headers;
        }

        public String getCookieHeader() {
            return cookieHeader;
        }
    }

    /**
     * 解析Apple库存API的JSON响应
     */
    private static StockResult parseStockResponse(String jsonResponse, long durationMs) {
        try {
            JsonNode rootNode = objectMapper.readTree(jsonResponse);

            // 检查响应结构
            if (!rootNode.has("body") || !rootNode.get("body").has("content")) {
                return new StockResult(false, "响应格式异常", null, durationMs);
            }

            JsonNode contentNode = rootNode.get("body").get("content");
            if (!contentNode.has("pickupMessage")) {
                return new StockResult(false, "无库存信息", null, durationMs);
            }

            JsonNode pickupMessage = contentNode.get("pickupMessage");
            if (!pickupMessage.has("stores")) {
                return new StockResult(false, "无门店信息", null, durationMs);
            }

            // 解析门店库存信息
            boolean hasStock = false;
            StringBuilder stockInfo = new StringBuilder();

            for (JsonNode store : pickupMessage.get("stores")) {
                String storeName = store.get("storeName").asText();
                String storeNumber = store.get("storeNumber").asText();
                boolean isAvailable = store.get("partsAvailability").get("MYEV3CH/A").get("pickupDisplay").asText().contains("available");

                stockInfo.append(String.format("🏪 %s (%s): %s%n",
                    storeName,
                    storeNumber,
                    isAvailable ? "✅ 有货" : "❌ 无货"));

                if (isAvailable) {
                    hasStock = true;
                }
            }

            return new StockResult(hasStock, stockInfo.toString().trim(), pickupMessage, durationMs);

        } catch (Exception e) {
            System.err.println("❌ 解析JSON响应失败: " + e.getMessage());
            return new StockResult(false, "解析失败: " + e.getMessage(), null, durationMs);
        }
    }

    /**
     * 库存检查结果类
     */
    public static class StockResult {
        private final boolean hasStock;
        private final String message;
        private final JsonNode rawData;
        private final long durationMs;

        public StockResult(boolean hasStock, String message, JsonNode rawData) {
            this(hasStock, message, rawData, -1);
        }

        public StockResult(boolean hasStock, String message, JsonNode rawData, long durationMs) {
            this.hasStock = hasStock;
            this.message = message;
            this.rawData = rawData;
            this.durationMs = durationMs;
        }

        public boolean hasStock() {
            return hasStock;
        }

        public String getMessage() {
            return message;
        }

        public JsonNode getRawData() {
            return rawData;
        }

        public long getDurationMs() {
            return durationMs;
        }

        @Override
        public String toString() {
            String durationInfo = durationMs > 0 ? String.format("⏱️  检查耗时: %dms%n", durationMs) : "";
            return String.format("库存状态: %s%n%s%s",
                hasStock ? "✅ 有货" : "❌ 无货",
                durationInfo,
                message);
        }
    }

    /**
     * 创建示例auth文件
     */
    private static void createExampleAuthFile(Path authFile) {
        try {
            // 创建基本的auth文件结构
            ObjectNode rootNode = objectMapper.createObjectNode();

            // 添加cookies数组（空的）
            rootNode.set("cookies", objectMapper.createArrayNode());

            // 添加origins数组（空的）
            rootNode.set("origins", objectMapper.createArrayNode());

            // 添加httpStockChecker配置
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            httpStockCheckerNode.put("url", "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=R670&little=false&parts.0=MYEV3CH/A&mts.0=regular&mts.1=sticky&fts=true");
            httpStockCheckerNode.put("referer", "https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            ObjectNode headersNode = objectMapper.createObjectNode();
            headersNode.put("Accept", "*/*");
            headersNode.put("Accept-Encoding", "gzip, deflate, br, zstd");
            headersNode.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            headersNode.put("Sec-Ch-Ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"");
            headersNode.put("Sec-Ch-Ua-Mobile", "?0");
            headersNode.put("Sec-Ch-Ua-Platform", "\"macOS\"");
            headersNode.put("Sec-Fetch-Dest", "empty");
            headersNode.put("Sec-Fetch-Mode", "cors");
            headersNode.put("Sec-Fetch-Site", "same-origin");
            headersNode.put("Priority", "u=1, i");
            headersNode.put("x-aos-ui-fetch-call-1", "9qh5nntdsi-mg4bxw0e");
            headersNode.put("x-skip-redirect", "true");
            httpStockCheckerNode.set("headers", headersNode);

            rootNode.set("httpStockChecker", httpStockCheckerNode);

            // 确保父目录存在
            if (authFile.getParent() != null) {
                Files.createDirectories(authFile.getParent());
            }

            // 写入文件
            Files.writeString(authFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
            System.out.println("✅ 已创建示例auth文件: " + authFile.toAbsolutePath());

        } catch (Exception e) {
            System.err.println("❌ 创建示例auth文件失败: " + e.getMessage());
        }
    }

    /**
     * 自动添加httpStockChecker配置到现有auth文件
     */
    private static boolean addHttpStockCheckerConfig(Path authFile) {
        try {
            // 读取现有文件
            String content = Files.readString(authFile);
            JsonNode rootNode = objectMapper.readTree(content);

            // 检查是否已经是ObjectNode
            if (!(rootNode instanceof ObjectNode)) {
                return false;
            }

            ObjectNode objectNode = (ObjectNode) rootNode;

            // 添加httpStockChecker配置
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            httpStockCheckerNode.put("url", "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=R670&little=false&parts.0=MYEV3CH/A&mts.0=regular&mts.1=sticky&fts=true");
            httpStockCheckerNode.put("referer", "https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            ObjectNode headersNode = objectMapper.createObjectNode();
            headersNode.put("Accept", "*/*");
            headersNode.put("Accept-Encoding", "gzip, deflate, br, zstd");
            headersNode.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            headersNode.put("Sec-Ch-Ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"");
            headersNode.put("Sec-Ch-Ua-Mobile", "?0");
            headersNode.put("Sec-Ch-Ua-Platform", "\"macOS\"");
            headersNode.put("Sec-Fetch-Dest", "empty");
            headersNode.put("Sec-Fetch-Mode", "cors");
            headersNode.put("Sec-Fetch-Site", "same-origin");
            headersNode.put("Priority", "u=1, i");
            headersNode.put("x-aos-ui-fetch-call-1", "9qh5nntdsi-mg4bxw0e");
            headersNode.put("x-skip-redirect", "true");
            httpStockCheckerNode.set("headers", headersNode);

            objectNode.set("httpStockChecker", httpStockCheckerNode);

            // 写回文件
            Files.writeString(authFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
            System.out.println("✅ 已自动添加httpStockChecker配置到: " + authFile.toAbsolutePath());
            return true;

        } catch (Exception e) {
            System.err.println("❌ 自动添加httpStockChecker配置失败: " + e.getMessage());
            return false;
        }
    }
}
