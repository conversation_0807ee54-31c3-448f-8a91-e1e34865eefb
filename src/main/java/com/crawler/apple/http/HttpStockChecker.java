package com.crawler.apple.http;

import java.net.CookieManager;
import java.net.CookiePolicy;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.Properties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class HttpStockChecker {

    // 基础Cookie配置 - 这些值可能需要定期更新
    private static final String BASE_COOKIES =
        "dssid2=ae0c7288-0f91-4d02-910c-3aeff16e6110; " +
        "dssf=1; " +
        "geo=CN; " +
        "as_sfa=Mnxjbnxjbnx8emhfQ058Y29uc3VtZXJ8aW50ZXJuZXR8MHwwfDE; " +
        "s_cc=true; " +
        "dslang=CN-ZH; " +
        "site=CHN; " +
        "as_dc=ucp7; " +
        "s_fid=57A6FC8DA3DACE21-158BBECBB3465547";

    // 可能过期的会话Cookie - 需要从实际会话中获取
    private static final String SESSION_COOKIES =
        "as_pcts=fe8pyyhZjcvTmcpsEY7sCg2g2fYLIsELNYBPvwwWCdabsuyniyIx3DjRPlWXRxCsC4u+xG+r69Eh5HlneUEU+djkQee9UR1LFSEG5i3KpyyzH+KeJF547xrvFL0vKT3jG9R46j5IAAELVVJxW9yP:-sygQOUEmzYY9Axe9mF1qRlzD++yiW7BwHtCvzxKSI1D0pYJI46n73Ol_lStQbAn; " +
        "as_gloc=edfeac71ad69d6e9fe2e8c34caa0d6489775e40c787dcb31611258f9e2979541b5450b74579b83ce14dc2ba4449521b279b174c543545f090f8db76a0fa994d16fc3ea195b862ac4854f4b4f2eee349f18aa63d9ec4f1ffaaa70c628baaf92907bfef73f640f9deb2ad626a64e0ec5ce0e3439e903d41413c14e9407c93f9210; " +
        "as_cn=~ZquYQACcP88V74rbcF1bFkW95X6LDC1vTBgVUm8RHWs=; " +
        "as_disa=AAAjAAAB6p8vhR9JqT9URnnWv3eQ2DDtmmUUvNSK9xk2Ji8DZGhGL5ky7XX5TUlKuGYreFOkAAIBJClxUnKRb8art3zof5l2oCyHkOtlTTIwAuD4qlZCE4Y=; " +
        "as_rec=f86ef18c66350e9b116060c115cf5afec8b44667ce011a19945b61606de71c1cb6d3f4842f328d61bd4d5cda73811aa9d96d2d7a158467ab1f9ad4f26f54d994f8f1d85edd9070ad051a555ad7294486; " +
        "as_rumid=541abfef-ee07-4424-83fe-f0468b82afed; " +
        "as_atb=1.0|MjAyNS0wOS0yOCAwNjo0NToxMA|9354873b0c193890d753a6fab2ef1b35fd0c7bf3";

    private static final String COOKIE_HEADER_VALUE = BASE_COOKIES + "; " + SESSION_COOKIES;

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static String currentCookieHeader = COOKIE_HEADER_VALUE;

    public static void main(String[] args) throws Exception {
        // 尝试加载配置文件中的cookie
        loadConfig();

        if (args.length < 1) {
            System.out.println("🍎 Apple库存检查工具");
            System.out.println("用法: java HttpStockChecker <商品编号> [商店代码]");
            System.out.println("     java HttpStockChecker --create-config  (创建配置文件)");
            System.out.println("示例: java HttpStockChecker MYEV3CH/A R670");
            System.out.println();
            System.out.println("支持的商品编号示例:");
            System.out.println("  MYEV3CH/A - iPhone 16 128GB 黑色");
            System.out.println("  MYEV3CH/A - iPhone 16 128GB 白色");
            System.out.println();
            System.out.println("常见商店代码:");
            System.out.println("  R670 - Apple 昆明");
            System.out.println("  R639 - 上海浦东店");
            System.out.println("  R610 - 深圳益田店");
            System.out.println();
            System.out.println("⚠️  注意: Cookie可能已过期，如遇541错误请更新cookie配置");
            return;
        }

        if (args[0].equals("--create-config")) {
            createExampleConfig();
            return;
        }

        String productCode = args[0];
        String storeCode = args.length > 1 ? args[1] : "R670";

        checkStock(productCode, storeCode);
    }

    public static void checkStock(String productCode, String storeCode) {
        String url = String.format(
            "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=%s&little=false&parts.0=%s&mts.0=regular&mts.1=sticky&fts=true",
            storeCode, productCode
        );

        try {
            HttpClient client = createHttpClient();
            HttpRequest request = buildRequest(url);

            System.out.println("🔍 检查商品库存: " + productCode + " 在商店: " + storeCode);
            System.out.println("📡 请求URL: " + url);

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            analyzeResponse(response, productCode, storeCode);

        } catch (Exception e) {
            System.err.println("❌ 请求失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static CompletableFuture<Void> checkStockAsync(String productCode, String storeCode) {
        return CompletableFuture.runAsync(() -> checkStock(productCode, storeCode));
    }

    private static HttpClient createHttpClient() {
        CookieManager cookieManager = new CookieManager();
        cookieManager.setCookiePolicy(CookiePolicy.ACCEPT_ALL);

        return HttpClient.newBuilder()
            .cookieHandler(cookieManager)
            .version(HttpClient.Version.HTTP_2)
            .connectTimeout(Duration.ofSeconds(10))
            .followRedirects(HttpClient.Redirect.NORMAL)
            .build();
    }

    private static HttpRequest buildRequest(String url) {
        return HttpRequest.newBuilder()
            .uri(URI.create(url))
            .timeout(Duration.ofSeconds(15))
            .header("Accept", "*/*")
            .header("Accept-Encoding", "gzip, deflate, br, zstd")
            .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
            .header("Referer", "https://www.apple.com.cn/shop/buy-iphone/iphone-16")
            .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
            .header("Sec-Ch-Ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"")
            .header("Sec-Ch-Ua-Mobile", "?0")
            .header("Sec-Ch-Ua-Platform", "\"macOS\"")
            .header("Sec-Fetch-Dest", "empty")
            .header("Sec-Fetch-Mode", "cors")
            .header("Sec-Fetch-Site", "same-origin")
            .header("Priority", "u=1, i")
            .header("x-aos-ui-fetch-call-1", "9qh5nntdsi-mg4bxw0e")
            .header("x-skip-redirect", "true")
            .header("Cookie", currentCookieHeader)
            .GET()
            .build();
    }

    private static void analyzeResponse(HttpResponse<String> response, String productCode, String storeCode) {
        System.out.println("\n📊 响应状态: " + response.statusCode());

        if (response.statusCode() == 200) {
            try {
                JsonNode rootNode = objectMapper.readTree(response.body());
                parseStockInfo(rootNode, productCode, storeCode);
            } catch (Exception e) {
                System.err.println("❌ JSON解析失败: " + e.getMessage());
                System.out.println("原始响应内容:");
                System.out.println(response.body().substring(0, Math.min(500, response.body().length())));
            }
        } else {
            System.err.println("❌ 请求失败，状态码: " + response.statusCode());
            System.out.println("响应头:");
            response.headers().map().forEach((k, v) -> System.out.println(k + ": " + v));
        }
    }

    private static void parseStockInfo(JsonNode rootNode, String productCode, String storeCode) {
        try {
            JsonNode bodyNode = rootNode.path("body");
            if (bodyNode.isMissingNode()) {
                System.out.println("⚠️ 响应中未找到body节点");
                return;
            }

            JsonNode contentNode = bodyNode.path("content");
            if (contentNode.isMissingNode()) {
                System.out.println("⚠️ 响应中未找到content节点");
                return;
            }

            JsonNode pickupMessageNode = contentNode.path("pickupMessage");
            if (pickupMessageNode.isMissingNode()) {
                System.out.println("⚠️ 响应中未找到pickupMessage节点");
                return;
            }

            JsonNode storesNode = pickupMessageNode.path("stores");
            if (!storesNode.isArray()) {
                System.out.println("⚠️ stores不是数组格式");
                return;
            }

            boolean hasStock = false;
            for (JsonNode storeNode : storesNode) {
                String storeNumber = storeNode.path("storeNumber").asText();
                if (!storeCode.equals(storeNumber)) {
                    continue;
                }

                JsonNode partsAvailabilityNode = storeNode.path("partsAvailability");
                if (partsAvailabilityNode.isMissingNode()) {
                    continue;
                }

                JsonNode productAvailabilityNode = partsAvailabilityNode.path(productCode);
                if (productAvailabilityNode.isMissingNode()) {
                    continue;
                }

                JsonNode pickupSearchQuoteNode = productAvailabilityNode.path("pickupSearchQuote");
                if (pickupSearchQuoteNode.isMissingNode()) {
                    continue;
                }

                String pickupQuote = pickupSearchQuoteNode.asText();
                boolean isAvailable = productAvailabilityNode.path("pickupDisplay").asText().contains("available");

                System.out.println("🏪 商店: " + storeNumber);
                System.out.println("📦 商品: " + productCode);
                System.out.println("📝 取货信息: " + pickupQuote);
                System.out.println("✅ 是否有货: " + (isAvailable ? "是" : "否"));

                if (isAvailable) {
                    hasStock = true;
                    System.out.println("🎉 库存状态: ✅ 有货！");
                } else {
                    System.out.println("😞 库存状态: ❌ 无货");
                }

                // 显示更多详细信息
                String storeName = storeNode.path("storeName").asText();
                String city = storeNode.path("city").asText();
                System.out.println("📍 商店名称: " + storeName + " (" + city + ")");

                break;
            }

            if (!hasStock) {
                System.out.println("⚠️ 未找到商品 " + productCode + " 在商店 " + storeCode + " 的库存信息");
            }

        } catch (Exception e) {
            System.err.println("❌ 解析库存信息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从配置文件加载最新的cookie设置
     */
    private static void loadConfig() {
        try {
            // 尝试从多个位置加载配置文件
            String[] configPaths = {
                "apple-stock-config.properties",
                "config/apple-stock-config.properties",
                "src/main/resources/apple-stock-config.properties"
            };

            Properties props = new Properties();
            boolean configLoaded = false;

            for (String configPath : configPaths) {
                Path path = Paths.get(configPath);
                if (Files.exists(path)) {
                    try (var reader = Files.newBufferedReader(path)) {
                        props.load(reader);
                        configLoaded = true;
                        System.out.println("✅ 已从 " + configPath + " 加载配置");
                        break;
                    }
                }
            }

            if (configLoaded) {
                // 从配置文件加载cookie
                String customCookies = props.getProperty("apple.cookies");
                if (customCookies != null && !customCookies.trim().isEmpty()) {
                    currentCookieHeader = customCookies.trim();
                    System.out.println("🔄 已加载自定义Cookie配置");
                }

                // 从配置文件加载其他设置
                String timeoutStr = props.getProperty("http.timeout.seconds", "15");
                String retryDelayStr = props.getProperty("retry.delay.ms", "500");

                System.out.println("⚙️  配置加载完成");
            } else {
                System.out.println("ℹ️  未找到配置文件，使用默认配置");
                System.out.println("💡 建议创建 apple-stock-config.properties 文件来配置最新的cookie");
            }

        } catch (Exception e) {
            System.err.println("⚠️  加载配置文件失败: " + e.getMessage());
            System.out.println("ℹ️  使用默认配置继续");
        }
    }

    /**
     * 更新当前会话的cookie
     */
    public static void updateCookies(String newCookies) {
        currentCookieHeader = newCookies;
        System.out.println("🔄 Cookie已更新");
    }

    /**
     * 批量检查多个商品的库存
     */
    public static void checkMultipleProducts(String[] productCodes, String storeCode) {
        System.out.println("🔄 开始批量检查库存...");

        for (String productCode : productCodes) {
            checkStock(productCode, storeCode);
            System.out.println("─".repeat(50));
            try {
                TimeUnit.MILLISECONDS.sleep(500); // 避免请求过于频繁
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 创建示例配置文件
     */
    public static void createExampleConfig() throws Exception {
        String exampleConfig = """
            # Apple库存检查工具配置文件
            # 复制最新的cookie字符串到这里
            apple.cookies=dssid2=your-dssid2-value; dssf=1; geo=CN; ...

            # HTTP请求超时时间（秒）
            http.timeout.seconds=15

            # 批量请求间隔（毫秒）
            retry.delay.ms=500
            """;

        Files.writeString(Paths.get("apple-stock-config.properties"), exampleConfig);
        System.out.println("✅ 已创建示例配置文件: apple-stock-config.properties");
        System.out.println("📝 请编辑该文件并填入最新的cookie信息");
    }
}
