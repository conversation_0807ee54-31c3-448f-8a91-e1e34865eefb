package com.crawler.apple.http;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.net.CookieManager;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class HttpStartCheckout {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String DEFAULT_SESSION_FILE = "/Users/<USER>/Desktop/applecrawer/apple-auth.json";
    private static final Path SESSION_FILE = Paths.get(
        System.getProperty("httpStartCheckout.sessionFile", DEFAULT_SESSION_FILE)
    );

    public static void main(String[] args) throws Exception {

        // --- 1. 准备阶段：从您的 trace 文件和 auth.json 文件中提取所有信息 ---

        // 从您捕获的请求头中原样复制
        String url = "https://www.apple.com.cn/shop/bagx/checkout_now?_a=checkout&_m=shoppingCart.actions";
        String cookieHeaderValue = buildCookieHeader(loadAuthRoot());
        String referer = "https://www.apple.com.cn/shop/bag";
        String userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
        String secChUa = "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"";
        String securityToken = resolveSecurityToken(loadAuthRoot(), "88WNzSHM_nZZSgpjSPo6nEFxiPvqqY44Yh5aEfNnfJA");

        // 动态读取购物袋 itemId，并构建 POST Body 中的数量字段
        JsonNode authRoot = loadAuthRoot();
        String cartItemId = extractCartItemId(authRoot);
        if (cartItemId == null || cartItemId.isBlank()) {
            // 兜底：保持旧的硬编码（避免中断）
            cartItemId = "269c8ad8-fead-46f5-b37e-fbf8d462dd54";
        }

        // 仅替换数量字段对应的 itemId，其他字段保持原样（源自 trace）
        String postBody = String.format(
            "shoppingCart.recommendations.recommendedItem.part=&shoppingCart.bagSavedItems.part=&shoppingCart.bagSavedItems.listId=&shoppingCart.bagSavedItems.childPart=&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.isIntentToGift=false&shoppingCart.items.item-%s.itemQuantity.quantity=1&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.city=昆明&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.state=云南&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.provinceCityDistrict=云南 昆明 五华区&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.countryCode=CN&shoppingCart.items.item-42b23c8c-05da-4bb7-bfeb-3c27385741e7.delivery.lineDeliveryOptions.address.provinceCityDistrictTabs.district=五华区&shoppingCart.locationConsent.locationConsent=false&shoppingCart.summary.promoCode.promoCode=&shoppingCart.actions.fcscounter=&shoppingCart.actions.fcsdata=",
            cartItemId
        );

        // --- 2. 创建 HTTP 客户端 ---
        // 注意：对于302重定向，我们需要让客户端自动跟随，或者手动处理
        HttpClient client = HttpClient.newBuilder()
            .cookieHandler(new CookieManager()) // 自动处理后续的 Set-Cookie
            .followRedirects(HttpClient.Redirect.NORMAL) // ** 让客户端自动处理重定向 **
            .version(HttpClient.Version.HTTP_2)
            .connectTimeout(Duration.ofSeconds(10))
            .build();

        // --- 3. 构建 POST 请求，精确复刻所有捕获到的请求头 ---
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .timeout(Duration.ofSeconds(20))
            .header("accept", "*/*")
            .header("accept-language", "zh-CN,zh;q=0.9,en;q=0.8")
            .header("content-type", "application/x-www-form-urlencoded")
            .header("cookie", cookieHeaderValue)
            .header("origin", "https://www.apple.com.cn")
            .header("referer", referer)
            .header("sec-ch-ua", secChUa)
            .header("sec-ch-ua-mobile", "?0")
            .header("sec-ch-ua-platform", "\"macOS\"")
            .header("sec-fetch-dest", "empty")
            .header("sec-fetch-mode", "cors")
            .header("sec-fetch-site", "same-origin")
            .header("user-agent", userAgent)
            .header("x-aos-model-page", "cart")
            .header("x-aos-stk", securityToken)
            .header("x-requested-with", "Fetch")
            .POST(HttpRequest.BodyPublishers.ofString(postBody))
            .build();

        // --- 4. 发送请求并获取最终的响应 ---
        System.out.println("正在发送“开始结账”的 POST 请求...");
        // 因为设置了自动重定向，这里的 response 会是跳转后的最终页面的响应
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

        // --- 5. 分析结果 ---
        System.out.println("Final Status Code: " + response.statusCode());
        System.out.println("Final URL: " + response.uri());
        // 打印关键响应头，便于诊断
        System.out.println("Location Header: " + response.headers().firstValue("location").orElse("<none>"));
        System.out.println("Set-Cookie Count: " + response.headers().allValues("set-cookie").size());

        // Body 只截取前 1500 字符，避免控制台过大
        String body = response.body() == null ? "" : response.body();
        String bodySnippet = body.length() > 1500 ? body.substring(0, 1500) + "..." : body;
        System.out.println("Response Body Snippet:\n" + bodySnippet);

        boolean bodyIndicatesCheckout = body.contains("/shop/checkout") || body.contains("\"redirectUrl\"\s*:\s*\"/shop/checkout");
        if (response.statusCode() == 200 && (response.uri().toString().contains("/shop/checkout") || bodyIndicatesCheckout)) {
            System.out.println("\n✅ 恭喜！请求成功并已重定向到结账页面！");
            System.out.println("下一步，您需要解析这个响应的 HTML，找到下一个操作所需的 x-aos-stk 令牌。");
        } else {
            System.out.println("\n❌ 请求失败！请检查：");
            System.out.println("1. Cookie 字符串是否完整且未过期。");
            System.out.println("2. 所有请求头是否都已原样复制。");
            System.out.println("3. POST Body 是否确实为空（请再次检查 trace.zip 的 Body/Payload 标签页）。");
            System.out.println("4. x-aos-stk 是否为当前会话的最新值（通常会变化）。");
            System.out.println("5. 如果是 AJAX 流程，可能返回 JSON/HTML 提示或 redirectUrl，请根据 Body Snippet 判断。");
        }
    }

    private static String resolveSecurityToken(JsonNode root, String fallback) {
        try {
            if (root != null) {
                JsonNode headers = root.path("httpHeaders");
                if (headers != null && headers.isObject()) {
                    JsonNode stk = headers.get("x-aos-stk");
                    if (stk != null && stk.isTextual() && !stk.asText().isBlank()) {
                        return stk.asText();
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return fallback;
    }

    private static String extractCartItemId(JsonNode root) {
        try {
            JsonNode lastItem = root.path("shoppingCart").path("lastItem");
            if (lastItem.isObject()) {
                JsonNode idNode = lastItem.get("itemId");
                if (idNode != null && idNode.isTextual() && !idNode.asText().isBlank()) {
                    return idNode.asText();
                }
                // 尝试从 elementId 解析
                JsonNode elementIdNode = lastItem.get("elementId");
                if (elementIdNode != null && elementIdNode.isTextual()) {
                    String el = elementIdNode.asText();
                    String prefix = "shoppingCart.items.item-";
                    int s = el.indexOf(prefix);
                    if (s >= 0) {
                        String tail = el.substring(s + prefix.length());
                        int sep = tail.indexOf('.') >= 0 ? tail.indexOf('.') : tail.indexOf('-');
                        if (sep > 0) {
                            tail = tail.substring(0, sep);
                        }
                        return tail;
                    }
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    private static JsonNode loadAuthRoot() throws IOException {
        if (!Files.exists(SESSION_FILE)) {
            throw new IllegalStateException("会话文件不存在: " + SESSION_FILE.toAbsolutePath());
        }
        try (java.io.BufferedReader reader = Files.newBufferedReader(SESSION_FILE)) {
            return objectMapper.readTree(reader);
        } catch (IOException e) {
            throw new IOException("读取会话文件失败: " + SESSION_FILE.toAbsolutePath(), e);
        }
    }

    private static String buildCookieHeader(JsonNode rootNode) {
        JsonNode cookiesNode = rootNode.path("cookies");
        if (cookiesNode == null || !cookiesNode.isArray()) {
            throw new IllegalStateException("apple-auth.json 缺少 cookies 数组");
        }

        StringBuilder cookieHeader = new StringBuilder();
        for (JsonNode cookie : cookiesNode) {
            JsonNode nameNode = cookie.get("name");
            JsonNode valueNode = cookie.get("value");
            if (nameNode == null || valueNode == null) {
                continue;
            }
            if (cookieHeader.length() > 0) {
                cookieHeader.append("; ");
            }
            cookieHeader.append(nameNode.asText()).append("=").append(valueNode.asText());
        }

        if (cookieHeader.length() == 0) {
            throw new IllegalStateException("会话文件中未找到可用的Cookie");
        }

        return cookieHeader.toString();
    }
}
