package com.crawler.apple;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.ApplePickupLocationConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.config.AppleWatchConfig;
import com.crawler.user.UserProfileManager;
import com.crawler.message.CrawlerMessage;
import java.util.concurrent.BlockingQueue;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.FrameLocator;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.PlaywrightException;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * Apple业务流程处理器 - 专为新架构设计
 * 使用传入的Page实例，不自己创建浏览器
 */
public class AppleBusinessFlow {
    private static final Logger logger = LoggerFactory.getLogger(AppleBusinessFlow.class);
    private static final String APPLE_ACCOUNT_HOME = "https://www.apple.com.cn/shop/account/home";

    private final Page page;
    private final AppleUserConfig userConfig;
    private final AppleIphoneConfig iphoneConfig;
    private final ApplePickupLocationConfig pickupLocationConfig;
    private final String username;
    private final String password;
    private final UserProfileManager userProfileManager;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final String screenshotDir;
    // 可选：消息队列引用，用于在关键步骤触发日志类消息
    private final BlockingQueue<CrawlerMessage> messageQueue;
    // 是否在门店页检测到"需要签到"并已选择时段
    private volatile boolean checkinRequiredDetected = false;

    // 存储捕获的fulfillment-messages请求headers
    private volatile Map<String, String> capturedFulfillmentHeaders = null;

    public AppleBusinessFlow(Page page, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig,
            ApplePickupLocationConfig pickupLocationConfig) {
        this.page = page;
        this.userConfig = userConfig;
        this.iphoneConfig = iphoneConfig;
        this.pickupLocationConfig = pickupLocationConfig;
        this.username = userConfig != null ? userConfig.getUsername() : null;
        this.password = userConfig != null ? userConfig.getPassword() : null;
        this.userProfileManager = new UserProfileManager();
        this.messageQueue = null; // 兼容旧构造方式

        logger.info("✅ AppleBusinessFlow 初始化完成，用户: {}",
                username != null ? username : "未配置用户名");

        String safeUsername = username != null && !username.isBlank() ? username : "unknown";
        this.screenshotDir = "users/" + safeUsername + "/screenshots";
    }

    // 新构造器：支持注入消息队列
    public AppleBusinessFlow(Page page, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig,
            ApplePickupLocationConfig pickupLocationConfig, BlockingQueue<CrawlerMessage> messageQueue) {
        this.page = page;
        this.userConfig = userConfig;
        this.iphoneConfig = iphoneConfig;
        this.pickupLocationConfig = pickupLocationConfig;
        this.username = userConfig != null ? userConfig.getUsername() : null;
        this.password = userConfig != null ? userConfig.getPassword() : null;
        this.userProfileManager = new UserProfileManager();
        this.messageQueue = messageQueue;

        logger.info("✅ AppleBusinessFlow 初始化完成，用户: {}",
                username != null ? username : "未配置用户名");

        String safeUsername = username != null && !username.isBlank() ? username : "unknown";
        this.screenshotDir = "users/" + safeUsername + "/screenshots";
    }

    /**
     * 设置捕获的fulfillment-messages请求headers
     *
     * @param headers 捕获的headers
     */
    public void setCapturedFulfillmentHeaders(Map<String, String> headers) {
        this.capturedFulfillmentHeaders = headers;
    }

    /**
     * 公开方法：保存浏览器会话状态到指定文件
     * 使用SessionFileManager确保配置不会丢失
     *
     * @param context     浏览器上下文
     * @param storageFile 保存文件路径
     */
    public void saveSessionStatePublic(BrowserContext context, Path storageFile) {
        saveSessionState(context, storageFile);
    }

    /**
     * 登录并生成会话文件 - 使用传入的Page实例
     */
    public void loginAndGenerateSession() {
        logger.info("开始执行登录流程以生成会话文件...");
        if (username == null || username.isBlank()) {
            throw new IllegalStateException("未配置用户名，无法执行登录流程");
        }
        logger.info("保存目标目录: users/{}/apple-auth.json", username);

        Path storageFile = Paths.get("users", username, "apple-auth.json");

        try {
            // 确保目录存在
            Path parentDir = storageFile.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            // 仅使用注入的 Page，不创建浏览器/上下文，避免与 CrawlerWorker 冲突
            page.navigate(APPLE_ACCOUNT_HOME);
            page.waitForLoadState(LoadState.NETWORKIDLE);

            // 确保进入登录页
            ensureSignInPage();

            // 执行登录流程
            login();

            // 保存会话状态（使用当前上下文）
            page.context().storageState(new BrowserContext.StorageStateOptions().setPath(storageFile));
            logger.info("✅ 会话文件已保存到: {}", storageFile.toAbsolutePath());

        } catch (Exception e) {
            logger.error("登录流程执行出错: {}", e.getMessage(), e);
            throw new IllegalStateException("登录流程执行出错", e);
        }
    }

    private void login() {
        logger.info("开始登录 Apple 账号...");
        String effectiveUsername = this.username;

        FrameLocator loginFrame = waitForLoginFrame();
        Locator accountField = loginFrame.locator("input#account_name_text_field");
        try {
            accountField.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(60_000));
        } catch (Exception e) {
            logger.error("用户名输入框未在预期时间内出现: {}", e.getMessage());
            throw new IllegalStateException("未能加载 Apple 登录用户名输入框", e);
        }
        if (effectiveUsername == null || effectiveUsername.trim().isEmpty()) {
            throw new IllegalStateException("用户名不能为空");
        }
        accountField.fill(effectiveUsername);
        logger.debug("已填写用户名");
        // 点击继续/登录按钮（Apple登录页用户名阶段）
        Locator firstSignIn = loginFrame.locator("button#sign-in");
        if (firstSignIn.isVisible()) {
            firstSignIn.click();
        }

        Locator passwordField = loginFrame.locator("input#password_text_field");
        try {
            passwordField.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(60_000));
        } catch (Exception e) {
            logger.error("密码输入框未在预期时间内出现: {}", e.getMessage());
            throw new IllegalStateException("未能加载 Apple 登录密码输入框", e);
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalStateException("密码不能为空");
        }
        passwordField.fill(password);
        logger.debug("已填写密码");
        // 点击登录按钮（密码阶段）
        Locator finalSignIn = loginFrame.locator("button#sign-in");
        finalSignIn.click();

        page.waitForLoadState(LoadState.NETWORKIDLE);
        logger.info("Apple 登录流程已提交");
    }

    private FrameLocator waitForLoginFrame() {
        try {
            // 兼容 Apple 登录 iframe 命名差异，放宽到 10 秒
            page.waitForSelector("iframe[name='aid-auth-widget-iFrame'], iframe[name^='aid-auth']",
                    new Page.WaitForSelectorOptions().setTimeout(10000));

            FrameLocator loginFrame;
            if (page.locator("iframe[name='aid-auth-widget-iFrame']").count() > 0) {
                loginFrame = page.frameLocator("iframe[name='aid-auth-widget-iFrame']");
            } else {
                loginFrame = page.frameLocator("iframe[name^='aid-auth']");
            }

            loginFrame.locator("input#account_name_text_field").waitFor(
                    new Locator.WaitForOptions().setTimeout(10000));

            logger.info("✅ 登录框架已加载");
            return loginFrame;

        } catch (Exception e) {
            logger.error("等待登录框架加载失败: {}", e.getMessage());
            throw new RuntimeException("登录框架加载失败", e);
        }
    }

    /**
     * 确保当前已进入 Apple 登录页。
     */
    private void ensureSignInPage() {
        try {
            String url = page.url();
            if (url != null && url.contains("signin.apple.com")) {
                return; // 已在登录页
            }

            Locator signInLink = page.locator("a[href*='signin.apple.com']");
            if (signInLink.count() > 0 && signInLink.first().isVisible()) {
                signInLink.first().click();
                page.waitForLoadState(LoadState.NETWORKIDLE);
            } else {
                Locator signInButton = page.locator("button:has-text('登录')");
                if (!signInButton.isVisible())
                    signInButton = page.locator("button:has-text('Sign in')");
                if (!signInButton.isVisible())
                    signInButton = page.locator("text=登录");
                if (!signInButton.isVisible())
                    signInButton = page.locator("text=Sign in");
                if (signInButton.isVisible()) {
                    signInButton.click();
                    page.waitForLoadState(LoadState.NETWORKIDLE);
                }
            }

            long start = System.currentTimeMillis();
            while (System.currentTimeMillis() - start < 30000) {
                if (page.url() != null && page.url().contains("signin.apple.com")) {
                    return;
                }
                try {
                    Thread.sleep(250);
                } catch (InterruptedException ignored) {
                }
            }
            logger.info("未检测到 signin.apple.com，继续等待登录 iframe...");
        } catch (Exception e) {
            logger.warn("确保登录页时发生非致命错误: {}", e.getMessage());
        }
    }

    /**
     * 检查iPhone17Pro库存
     */
    public boolean checkIphone17ProStock() {
        boolean defaultColorInStock = false;
        try {
            logger.info("开始监控 iPhone 17 Pro 昆明库存...");

            // 导航到iPhone购买页面
            page.navigate(iphoneConfig.getBuyUrl());
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            if (iphoneConfig.isRequiresModelSelection()) {
                // 选择iPhone型号 - 添加随机延迟
                Locator modelOption = page.locator("span.form-selector-title",
                        new Page.LocatorOptions().setHasText(iphoneConfig.getModelDisplayName()));
                modelOption.first().waitFor();
                modelOption.first().click();
                page.waitForTimeout(1000 + (long) (Math.random() * 1000)); // 1-2秒随机延迟
            } else {
                logger.info("配置指示该机型无需手动选择型号，跳过型号选择步骤。");
            }

            // 选择颜色 - 添加随机延迟
            logger.info("尝试选择颜色: {}", iphoneConfig.getColorDisplayName());
            // ConfigValidator.printUnicodeInfo(iphoneConfig.getColorDisplayName());

            page.getByRole(AriaRole.LISTITEM)
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getColorDisplayName()))
                    .locator("img")
                    .click();
            page.waitForTimeout(1500 + (long) (Math.random() * 1000)); // 1.5-2.5秒随机延迟

            // 选择存储容量 - 添加随机延迟
            page.locator("#root span")
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getStorageDisplayName()))
                    .nth(1)
                    .click();
            page.waitForTimeout(1200 + (long) (Math.random() * 800)); // 1.2-2秒随机延迟

            // 选择折抵换购选项 - 添加随机延迟
            if (iphoneConfig.isTradeIn()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("折抵换购")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不折抵换购")).check();
            }
            page.waitForTimeout(800 + (long) (Math.random() * 600)); // 0.8-1.4秒随机延迟

            // 选择AppleCare+选项 - 添加随机延迟
            if (iphoneConfig.isAppleCare()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            page.waitForTimeout(1000 + (long) (Math.random() * 800)); // 1-1.8秒随机延迟

            defaultColorInStock = checkKunmingPickupAvailability();
            if (defaultColorInStock) {
                return true;
            }

        } catch (Exception e) {
            logger.error("iPhone 17 Pro 选购流程执行失败: {}", e.getMessage(), e);
            // captureDebugScreenshot(page, "add-to-cart-failed");
        }

        if (!defaultColorInStock && checkAlternateColorStocks()) {
            return true;
        }

        return false; // 默认返回无库存
    }

    private boolean checkKunmingPickupAvailability() {
        logger.info("检查是否显示昆明取货信息...");
        Locator fulfillmentInfo = page.locator(".rf-bfe-summary-fulfillment-fullWidth");
        fulfillmentInfo
                .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

        logger.info("检查是否显示昆明取货信息...");
        // 等待并检查是否有昆明取货选项
        try {
            Locator kunmingButton = page.locator("button.rf-pickup-quote-overlay-trigger")
                    .filter(new Locator.FilterOptions().setHasText("Apple 昆明")).first();
            kunmingButton
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            logger.info("昆明取货选项出现，说明cookie正常未被拦截");

            // 检查是否显示"立即订购。取货 (店内)"标识
            Locator pickupLabel = page.locator("text=立即订购。取货 (店内)");
            pickupLabel
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(5000));
            logger.info("监控发现 iphone17'立即订购。取货 (店内)'标识，可以进行下单");
            return true; // 有库存

        } catch (Exception e) {
            logger.info("当前颜色无库存");
            // logger.debug("未找到完整的取货信息，无法进行下单: " + e.getMessage());
            // captureDebugScreenshot(page, "kunming-pickup-missing");
            return false;
        }
    }

    private boolean checkAlternateColorStocks() {
        if (iphoneConfig.getAlternateColors() == null || iphoneConfig.getAlternateColors().isEmpty()) {
            return false;
        }

        for (AppleIphoneConfig.ColorOption colorOption : iphoneConfig.getAlternateColors()) {
            if (colorOption == null || colorOption.getColorDisplayName() == null
                    || colorOption.getColorDisplayName().trim().isEmpty()) {
                continue;
            }

            if (attemptAlternateColorCheck(colorOption)) {
                return true;
            }
        }

        return false;
    }

    private boolean attemptAlternateColorCheck(AppleIphoneConfig.ColorOption colorOption) {
        String colorDisplayName = colorOption.getColorDisplayName();
        try {
            logger.info("尝试切换颜色并检查库存: {}", colorDisplayName);
            // ConfigValidator.printUnicodeInfo(colorDisplayName);

            page.getByRole(AriaRole.LISTITEM)
                    .filter(new Locator.FilterOptions().setHasText(colorDisplayName))
                    .locator("img")
                    .click();
            page.waitForTimeout(1500 + (long) (Math.random() * 1000)); // 1.5-2.5秒随机延迟

            page.locator("#root span")
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getStorageDisplayName()))
                    .nth(1)
                    .click();
            page.waitForTimeout(1200 + (long) (Math.random() * 800)); // 1.2-2秒随机延迟

            if (iphoneConfig.isTradeIn()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("折抵换购")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不折抵换购")).check();
            }
            page.waitForTimeout(800 + (long) (Math.random() * 600)); // 0.8-1.4秒随机延迟

            if (iphoneConfig.isAppleCare()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            page.waitForTimeout(1000 + (long) (Math.random() * 800)); // 1-1.8秒随机延迟

            return checkKunmingPickupAvailability();
        } catch (Exception e) {
            logger.warn("切换至颜色 {} 时发生异常: {}", colorDisplayName, e.getMessage());
            return false;
        }
    }

    /**
     * 加购物车流程（选取手机型号，计划，加入购物车）
     */
    public void addIphone17ProToCart() {
        try {
            logger.info("开始执行 iPhone 17 Pro 选购流程...");

            // 导航到iPhone购买页面
            page.navigate(iphoneConfig.getBuyUrl());
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            if (iphoneConfig.isRequiresModelSelection()) {
                // 选择iPhone型号 - 添加随机延迟
                Locator modelOption = page.locator("span.form-selector-title",
                        new Page.LocatorOptions().setHasText(iphoneConfig.getModelDisplayName()));
                modelOption.first().waitFor();
                modelOption.first().click();
                page.waitForTimeout(1000 + (long) (Math.random() * 1000)); // 1-2秒随机延迟
            } else {
                logger.info("配置指示该机型无需手动选择型号，跳过型号选择步骤。");
            }

            // 选择颜色 - 添加随机延迟
            logger.info("尝试选择颜色: {}", iphoneConfig.getColorDisplayName());
            // ConfigValidator.printUnicodeInfo(iphoneConfig.getColorDisplayName());

            page.getByRole(AriaRole.LISTITEM)
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getColorDisplayName()))
                    .locator("img")
                    .click();
            page.waitForTimeout(1500 + (long) (Math.random() * 1000)); // 1.5-2.5秒随机延迟

            // 选择存储容量 - 添加随机延迟
            page.locator("#root span")
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getStorageDisplayName()))
                    .nth(1)
                    .click();
            page.waitForTimeout(1200 + (long) (Math.random() * 800)); // 1.2-2秒随机延迟

            // 选择折抵换购选项 - 添加随机延迟
            if (iphoneConfig.isTradeIn()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("折抵换购")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不折抵换购")).check();
            }
            page.waitForTimeout(800 + (long) (Math.random() * 600)); // 0.8-1.4秒随机延迟

            // 选择AppleCare+选项 - 添加随机延迟
            if (iphoneConfig.isAppleCare()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            page.waitForTimeout(1000 + (long) (Math.random() * 800)); // 1-1.8秒随机延迟

            page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("添加到购物袋")).click();
            page.waitForTimeout(2000 + (long) (Math.random() * 1500)); // 2-3.5秒随机延迟
            logger.info("iPhone 17 Pro 已成功加入购物袋");
            page.waitForLoadState(LoadState.NETWORKIDLE);

        } catch (Exception e) {
            logger.error("iPhone 17 Pro 选购流程执行失败: {}", e.getMessage(), e);
            captureDebugScreenshot(page, "add-to-cart-failed");
        }
    }

    /**
     * 加购 Apple Watch Ultra 3。
     */
    public void addAppleWatchUltraToCart(AppleWatchConfig watchConfig) {
        if (watchConfig == null) {
            throw new IllegalArgumentException("Apple Watch 配置不能为空");
        }
        if (watchConfig.getBuyUrl() == null || watchConfig.getBuyUrl().isBlank()) {
            throw new IllegalArgumentException("Apple Watch 购买链接未配置");
        }

        try {
            logger.info("开始执行 Apple Watch Ultra 选购流程...");

            page.navigate(watchConfig.getBuyUrl());
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            randomDelay(1000, 1000);

            selectWatchCase(watchConfig);
            selectWatchBandStyle(watchConfig);
            selectWatchBandColor(watchConfig);
            selectWatchBandSize(watchConfig);
            selectTradeInOption(watchConfig.isTradeIn());
            logger.debug("准备选手表计划选项...");
            selectAppleCareOption(watchConfig.isAppleCare());
            logger.debug("选手表计划选项已选中...");

            page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("添加到购物袋")).click();
            randomDelay(2000, 1500);
            page.waitForLoadState(LoadState.NETWORKIDLE);

            logger.info("Apple Watch Ultra 已成功加入购物袋");

        } catch (Exception e) {
            logger.error("Apple Watch Ultra 选购流程执行失败: {}", e.getMessage(), e);
            captureDebugScreenshot(page, "add-watch-to-cart-failed");
            throw new IllegalStateException("Apple Watch Ultra 加入购物袋失败", e);
        }
    }

    /**
     * 清空购物袋，将所有商品移除。
     */
    public void clearShoppingBag() {
        logger.info("开始清空购物袋...");
        String bagUrl = resolveBagUrl();

        try {
            page.navigate(bagUrl);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            page.waitForLoadState(LoadState.NETWORKIDLE);
            randomDelay(1200, 800);

            if (!hasBagItems()) {
                logger.info("购物袋当前为空，无需处理");
                return;
            }

            removeAllBagItems();

            if (hasBagItems()) {
                logger.warn("清空购物袋后仍检测到商品，请人工确认");
            } else {
                logger.info("购物袋已全部清空");
            }

        } catch (Exception e) {
            logger.error("清空购物袋过程失败: {}", e.getMessage(), e);
            captureDebugScreenshot(page, "clear-bag-failed");
            throw new IllegalStateException("清空购物袋失败", e);
        }
    }

    private void selectWatchCase(AppleWatchConfig watchConfig) {
        if (isBlank(watchConfig.getCaseDisplayName()) && isBlank(watchConfig.getCaseValue())) {
            logger.info("未配置 Apple Watch 表壳选项，跳过");
            return;
        }

        try {
            if (!isBlank(watchConfig.getCaseDisplayName())) {
                logger.info("尝试选择表壳: {}", watchConfig.getCaseDisplayName());
                // ConfigValidator.printUnicodeInfo(watchConfig.getCaseDisplayName());

                Locator fieldset = page.locator("fieldset:has(#watch_cases-dimensionColor)");
                Locator optionLabel = fieldset.locator("label")
                        .filter(new Locator.FilterOptions().setHasText(watchConfig.getCaseDisplayName()));
                optionLabel.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE)
                        .setTimeout(10000));
                optionLabel.first().click();
            } else {
                selectRadioOptionByValue("watch_cases-dimensionColor", watchConfig.getCaseValue());
            }

            randomDelay(1500, 1000);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch 表壳失败", e);
        }
    }

    private void selectWatchBandStyle(AppleWatchConfig watchConfig) {
        if (isBlank(watchConfig.getBandStyleDisplayName()) && isBlank(watchConfig.getBandStyleValue())) {
            logger.info("未配置 Apple Watch 表带类型，跳过");
            return;
        }

        try {
            if (!isBlank(watchConfig.getBandStyleDisplayName())) {
                logger.info("尝试选择表带类型: {}", watchConfig.getBandStyleDisplayName());
                // ConfigValidator.printUnicodeInfo(watchConfig.getBandStyleDisplayName());

                Locator styleFieldset = page.locator("fieldset:has(#bandCategories)");
                Locator optionLabel = styleFieldset.locator("label.form-selector-label")
                        .filter(new Locator.FilterOptions().setHasText(watchConfig.getBandStyleDisplayName()));
                optionLabel.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE)
                        .setTimeout(10000));
                optionLabel.first().click();
            } else {
                selectRadioOptionByValue("style", watchConfig.getBandStyleValue());
            }

            randomDelay(1200, 800);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch 表带类型失败", e);
        }
    }

    private void selectWatchBandColor(AppleWatchConfig watchConfig) {
        if (isBlank(watchConfig.getBandColorDisplayName()) && isBlank(watchConfig.getBandColorValue())) {
            logger.info("未配置 Apple Watch 表带颜色，跳过");
            return;
        }

        try {
            if (!isBlank(watchConfig.getBandColorDisplayName())) {
                logger.info("尝试选择表带颜色: {}", watchConfig.getBandColorDisplayName());
                // ConfigValidator.printUnicodeInfo(watchConfig.getBandColorDisplayName());

                Locator colorFieldset = page.locator("fieldset[aria-labelledby='watch_bands-dimensionColor-label']");
                Locator optionLabel = colorFieldset.locator("label")
                        .filter(new Locator.FilterOptions().setHasText(watchConfig.getBandColorDisplayName()));
                optionLabel.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE)
                        .setTimeout(10000));
                optionLabel.first().click();
            } else {
                String groupName = resolveBandColorGroupName(watchConfig);
                if (groupName == null) {
                    throw new IllegalStateException("未能根据配置确定表带颜色选项组名称");
                }
                selectRadioOptionByValue(groupName, watchConfig.getBandColorValue());
            }

            randomDelay(1200, 800);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch 表带颜色失败", e);
        }
    }

    private void selectWatchBandSize(AppleWatchConfig watchConfig) {
        if (isBlank(watchConfig.getBandSizeDisplayName()) && isBlank(watchConfig.getBandSizeValue())) {
            logger.info("未配置 Apple Watch 表带尺码，跳过");
            return;
        }

        try {
            if (!isBlank(watchConfig.getBandSizeDisplayName())) {
                Locator sizeFieldset = page.locator("fieldset:has(#bandSize)");
                Locator optionLabel = sizeFieldset.locator("label.form-selector-label")
                        .filter(new Locator.FilterOptions().setHasText(watchConfig.getBandSizeDisplayName()));
                optionLabel.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE)
                        .setTimeout(10000));
                optionLabel.first().click();
            } else {
                selectRadioOptionByValue("watch_bands-dimensionbandsize", watchConfig.getBandSizeValue());
            }

            randomDelay(1000, 600);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch 表带尺码失败", e);
        }
    }

    private void selectTradeInOption(boolean tradeIn) {
        try {
            String keyword = tradeIn ? "添加折抵设备" : "不折抵换购";
            Locator optionLabel = page.locator("label")
                    .filter(new Locator.FilterOptions().setHasText(keyword));
            optionLabel.first().waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(10000));
            optionLabel.first().click();
            randomDelay(800, 600);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch 折抵换购选项失败", e);
        }
    }

    private void selectAppleCareOption(boolean appleCare) {
        try {
            // 与iPhone选购流程保持一致的选择方式
            if (appleCare) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            randomDelay(1000, 800);
        } catch (Exception e) {
            throw new IllegalStateException("选择 Apple Watch AppleCare+ 选项失败", e);
        }
    }

    private void selectRadioOptionByValue(String inputName, String inputValue) {
        if (isBlank(inputValue)) {
            return;
        }

        Locator radio = page.locator("input[name='" + inputName + "'][value='" + inputValue + "']");
        radio.waitFor(new Locator.WaitForOptions().setTimeout(10000));

        String id = radio.getAttribute("id");
        if (id != null) {
            Locator label = page.locator("label[for='" + id + "']");
            if (label.count() > 0) {
                label.first().click();
                return;
            }
        }

        try {
            radio.check(new Locator.CheckOptions().setForce(true));
        } catch (PlaywrightException e) {
            throw new IllegalStateException("强制选择选项失败: " + inputName + "=" + inputValue, e);
        }
    }

    private String resolveBandColorGroupName(AppleWatchConfig watchConfig) {
        if (!isBlank(watchConfig.getBandStyleValue())) {
            return watchConfig.getBandStyleValue() + "-color";
        }
        return null;
    }

    private void removeAllBagItems() {
        int safetyCounter = 0;
        while (true) {
            Locator items = page.locator("ol[data-autom='bag-items'] li[data-autom^='bag-item']");
            int itemCount = items.count();
            if (itemCount == 0) {
                return;
            }

            Locator currentItem = items.first();
            String itemName = "未知商品";
            try {
                Locator nameLocator = currentItem.locator("[data-autom='bag-item-name']");
                if (nameLocator.count() > 0) {
                    String text = nameLocator.first().textContent();
                    if (text != null) {
                        itemName = text.trim();
                    }
                }
            } catch (Exception ignore) {
                // 如果无法获取商品名称，继续执行移除
            }

            logger.info("移除购物袋商品: {} (剩余 {} 件)", itemName, itemCount);

            Locator removeButton = currentItem.locator("button[data-autom='bag-item-remove-button']");
            if (removeButton.count() == 0) {
                logger.warn("未找到移除按钮，终止清空流程");
                break;
            }
            removeButton.first().waitFor(
                    new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            removeButton.first().click();

            try {
                currentItem.waitFor(new Locator.WaitForOptions()
                        .setState(WaitForSelectorState.DETACHED)
                        .setTimeout(15000));
            } catch (PlaywrightException ex) {
                logger.warn("等待商品移除超时，尝试继续。原因: {}", ex.getMessage());
            }

            page.waitForLoadState(LoadState.NETWORKIDLE);
            randomDelay(800, 600);

            safetyCounter++;
            if (safetyCounter > 15) {
                logger.warn("移除购物袋商品达到安全阈值，终止循环");
                break;
            }
        }
    }

    private boolean hasBagItems() {
        try {
            return page.locator("ol[data-autom='bag-items'] li[data-autom^='bag-item']").count() > 0;
        } catch (Exception e) {
            logger.warn("检查购物袋商品时出错: {}", e.getMessage());
            return false;
        }
    }

    private String resolveBagUrl() {
        if (iphoneConfig != null && iphoneConfig.getCheckoutUrl() != null
                && !iphoneConfig.getCheckoutUrl().isBlank()) {
            return iphoneConfig.getCheckoutUrl();
        }
        return "https://www.apple.com.cn/shop/bag";
    }

    private void randomDelay(long baseMillis, long randomRangeMillis) {
        long delay = baseMillis;
        if (randomRangeMillis > 0) {
            delay += (long) (Math.random() * randomRangeMillis);
        }
        page.waitForTimeout(delay);
    }

    private boolean isBlank(String value) {
        return value == null || value.trim().isEmpty();
    }

    /**
     * 执行完整的下单流程（查看购物袋、调整数量、结账等）
     */
    public void executePurchaseFlow() {
        checkIphone17ProStock();
        logger.info("结算购买流程...");
        // 导航到购物袋或结账页
        String checkoutUrl = iphoneConfig != null && iphoneConfig.getCheckoutUrl() != null
                ? iphoneConfig.getCheckoutUrl()
                : "https://www.apple.com.cn/shop/bag";
        page.navigate(checkoutUrl);
        page.waitForLoadState(LoadState.DOMCONTENTLOADED);
        // page.waitForLoadState(LoadState.NETWORKIDLE);

        // 如果此时购物袋为空，则先加入 iPhone 到购物袋
        try {
            boolean bagEmptyBannerExists = page.locator("#bag-content .rs-bagempty h1").count() > 0
                    || page.getByText("你的购物袋中没有商品。").count() > 0;
            if (bagEmptyBannerExists || !hasBagItems()) {
                logger.info("检测到购物袋为空，尝试将 iPhone 17 Pro 加入购物袋...");
                addIphone17ProToCart();
                // 返回购物袋/结账页
                page.navigate(checkoutUrl);
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                page.waitForLoadState(LoadState.NETWORKIDLE);
            }
        } catch (Exception e) {
            logger.warn("检查购物袋是否为空时出错: {}", e.getMessage());
        }

        // 调整商品数量为2
        Locator quantitySelect = page.locator("select.rs-quantity-dropdown[id*='itemQuantity']");
        if (quantitySelect.count() > 0) {
            Locator firstDropdown = quantitySelect.first();
            firstDropdown.waitFor(
                    new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            firstDropdown.selectOption("2");
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("已通过下拉框将商品数量更新为 2");
        } else {
            Locator quantityInput = page.locator("input[id*='itemQuantity']");
            if (quantityInput.count() > 0) {
                Locator firstQuantityInput = quantityInput.first();
                firstQuantityInput.waitFor(
                        new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
                firstQuantityInput.fill("2");
                firstQuantityInput.press("Enter");
                page.waitForLoadState(LoadState.NETWORKIDLE);
                logger.info("已将商品数量更新为 2");
            }
        }

        // 结账
        logger.info("点击结账按钮...");
        page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("结账")).first().click();

        // 等待页面导航完成，使用更稳定的等待策略
        try {
            // 等待页面开始导航
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            // 额外等待确保页面完全加载
            page.waitForTimeout(2000 + (long) (Math.random() * 1000)); // 2-3秒随机延迟

            logger.info("检查页面是否跳转到登录页面...");
            // 检查是否跳转到了登录页面
            if (isLoginPage(page)) {
                logger.info("检测到登录页面，开始登录流程...");
                login();
                checkout(page);
            } else {
                logger.info("未检测到登录页面，继续结账流程...");
                checkout(page);
            }
        } catch (Exception e) {
            logger.warn("检查登录页面时出错: {}", e.getMessage());
            // 如果检查失败，尝试直接进行结账流程
            logger.info("尝试直接进行结账流程...");
            checkout(page);
        }
    }

    private void checkout(Page page) {
        try {
            logger.info("开始执行结账流程...");

            // 查找取货按钮 - 使用多种方式确保能找到
            Locator pickupButton = page.locator("button.rc-segmented-control-button")
                    .filter(new Locator.FilterOptions().setHasText("我要取货"));
            pickupButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

            // 检查按钮是否已经被选中
            String ariaChecked = pickupButton.getAttribute("aria-checked");
            if (ariaChecked == null || !ariaChecked.equals("true")) {
                logger.info("点击我要取货按钮...");
                pickupButton.click();
                page.waitForTimeout(1000); // 等待按钮点击效果
            } else {
                logger.info("我要取货按钮已经被选中，跳过点击");
            }

            logger.info("已处理我要取货选项...");

            Locator locationToggle = page
                    .locator("button[data-autom='fulfillment-pickup-store-search-button']");

            // 使用配置中的省份信息检查当前位置
            String targetProvince = pickupLocationConfig != null
                    && pickupLocationConfig.getProvinceDisplayName() != null
                            ? pickupLocationConfig.getProvinceDisplayName()
                            : "云南";
            boolean locationPreset = isTargetLocation(locationToggle, targetProvince);

            if (!locationPreset) {
                ensureLocationSelectorOpen(page, locationToggle);

                Locator tabList = page.locator(".rc-province-selector-tablist");
                tabList.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

                // 选择省份
                String province = pickupLocationConfig != null && pickupLocationConfig.getProvinceDisplayName() != null
                        ? pickupLocationConfig.getProvinceDisplayName()
                        : "云南";
                selectRegionOption(page, locationToggle, "state",
                        ".rc-province-selector-option-state", province);
                logger.info("选中{}...", province);

                // 选择城市
                String city = pickupLocationConfig != null && pickupLocationConfig.getCityDisplayName() != null
                        ? pickupLocationConfig.getCityDisplayName()
                        : "昆明";
                selectRegionOption(page, locationToggle, "city",
                        ".rc-province-selector-option-city", city);
                logger.info("选中{}...", city);

                // 选择区域
                String district = pickupLocationConfig != null && pickupLocationConfig.getDistrictDisplayName() != null
                        ? pickupLocationConfig.getDistrictDisplayName()
                        : "五华区";
                selectRegionOption(page, locationToggle, "district",
                        ".rc-province-selector-option-district", district);
                logger.info("选中{}...", district);
            } else {
                String province = pickupLocationConfig != null && pickupLocationConfig.getProvinceDisplayName() != null
                        ? pickupLocationConfig.getProvinceDisplayName()
                        : "云南";
                String city = pickupLocationConfig != null && pickupLocationConfig.getCityDisplayName() != null
                        ? pickupLocationConfig.getCityDisplayName()
                        : "昆明";
                logger.info("当前已处于{} {}，跳过地区选择。", province, city);
            }

            logger.info("地区选择完毕...");
            // page.waitForTimeout(5000);

            // 选择门店
            String storeName = pickupLocationConfig != null && pickupLocationConfig.getStoreDisplayName() != null
                    ? pickupLocationConfig.getStoreDisplayName()
                    : "Apple 昆明";
            String storeCode = pickupLocationConfig != null && pickupLocationConfig.getStoreCode() != null
                    ? pickupLocationConfig.getStoreCode()
                    : "R670";
            selectStoreOption(page, storeName, storeCode);

            logger.info("⏳ 等待门店选择后的页面加载...");
            // 等待页面完全加载
            page.waitForLoadState(LoadState.NETWORKIDLE);
            page.waitForTimeout(10000); // 额外等待10秒确保页面稳定

            logger.info("✅ 门店选择完成，开始进入取货详情页面...");

            // 重置签到标记
            checkinRequiredDetected = false;

            // 如果页面提示“需要签到”，则选择取货时段后再继续
            try {
                // 容器可能异步淡入，先等容器附加
                Locator multipleAvailability = page.locator(".rt-storelocator-store-multipleavailability");
                try {
                    multipleAvailability.waitFor(new Locator.WaitForOptions()
                            .setState(WaitForSelectorState.ATTACHED).setTimeout(10000));
                } catch (Exception ignore) {
                    logger.debug("等待‘需要签到’容器时出错: {}", ignore.getMessage());
                }

                // 提示文本：需要签到
                String indicatorText = (iphoneConfig != null && iphoneConfig.getCheckinIndicatorText() != null
                        && !iphoneConfig.getCheckinIndicatorText().isBlank())
                                ? iphoneConfig.getCheckinIndicatorText()
                                : "需要签到";
                Locator checkinHint = multipleAvailability.getByText(indicatorText);

                // 同时兜底检测：如果直接出现了时段下拉框，也视为需要签到
                Locator timeSlotDropdown = page.locator("select[data-autom='pickup-availablewindow-dropdown']");
                boolean dropdownPresent = timeSlotDropdown.count() > 0;

                if (checkinHint.count() > 0 || dropdownPresent) {
                    if (checkinHint.count() > 0) {
                        logger.info("检测到‘需要签到’提示，开始选择取货时段...");
                    } else {
                        logger.info("未见提示文案，但发现时段下拉框，按需选择时段...");
                    }

                    // 确认/等待下拉框可见
                    try {
                        timeSlotDropdown.waitFor(new Locator.WaitForOptions()
                                .setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
                    } catch (Exception e) {
                        // 有时 aria-hidden 切换较慢，再尝试定位一次
                        timeSlotDropdown = page.locator("select[data-autom='pickup-availablewindow-dropdown']");
                        timeSlotDropdown.waitFor(new Locator.WaitForOptions()
                                .setState(WaitForSelectorState.ATTACHED).setTimeout(15000));
                    }

                    int optionCount = timeSlotDropdown.locator("option").count();
                    if (optionCount > 1) { // 第0个通常是“可选时段”（占位/禁用）
                        Locator firstTimeSlot = timeSlotDropdown.locator("option").nth(1);
                        String timeSlotValue = firstTimeSlot.getAttribute("value");
                        if (timeSlotValue != null && !timeSlotValue.isEmpty()) {
                            timeSlotDropdown.selectOption(timeSlotValue);
                            logger.info("已选择第一个可用时段: {}", firstTimeSlot.innerText());
                            page.waitForTimeout(800);
                        } else {
                            logger.warn("第一个可用时段无有效值，跳过选择");
                        }
                    } else {
                        logger.warn("未找到可用时段选项，跳过选择");
                    }
                    checkinRequiredDetected = true;
                } else {
                    logger.info("未检测到‘需要签到’提示，跳过时段选择");
                }
            } catch (Exception e) {
                logger.debug("处理‘需要签到’与时段选择时出现异常，继续执行: {}", e.getMessage());
            }

            Locator continueButton = page.getByRole(AriaRole.BUTTON,
                    new Page.GetByRoleOptions().setName("继续填写取货详情"));
            continueButton.waitFor(
                    new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            continueButton.scrollIntoViewIfNeeded();
            continueButton.click();
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("结账流程执行完毕，已进入下一步");

            fillPickupDetails(page);

        } catch (Exception e) {
            logger.warn("结帐流程执行失败: {}", e.getMessage(), e);
        }
    }

    private void fillPickupDetails(Page page) {
        try {
            logger.info("📝 开始填写取货详情...");

            // 等待页面完全加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            page.waitForLoadState(LoadState.NETWORKIDLE);
            page.waitForTimeout(1000); // 额外等待确保页面稳定

            logger.info("🔍 等待取货详情表单加载...");
            Locator lastName = page.locator("input[name='lastName']");
            lastName.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到姓氏输入框，开始填写...");
            lastName.fill(userConfig.getLastName());

            Locator firstName = page.locator("input[name='firstName']");
            firstName.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到名字输入框，开始填写...");
            firstName.fill(userConfig.getFirstName());

            Locator phone = page.locator("input[name='fullDaytimePhone']");
            phone.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到电话输入框，开始填写...");
            phone.fill(userConfig.getPhone());

            Locator email = page.locator("input[name='emailAddress']");
            email.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            email.fill(userConfig.getEmail());

            // 若上一页检测到需要签到，则填写身份证后四位
            if (checkinRequiredDetected) {
                try {
                    String last4 = null;
                    if (userConfig != null) {
                        if (userConfig.getIdCardLast4() != null && !userConfig.getIdCardLast4().isBlank()) {
                            last4 = userConfig.getIdCardLast4().trim();
                        } else if (userConfig.getIdCardNumber() != null && userConfig.getIdCardNumber().length() >= 4) {
                            String idNum = userConfig.getIdCardNumber();
                            last4 = idNum.substring(idNum.length() - 4);
                            logger.info("未配置 idCardLast4，已从完整证件号中推导后四位");
                        }
                    }

                    if (last4 != null && last4.length() == 4) {
                        Locator idLast4Input = page.locator("input[data-autom='form-field-nationalIdSelf']");
                        if (idLast4Input.count() == 0) {
                            // 备用选择器（ID 需要转义点号）
                            idLast4Input = page.locator(
                                    "input#checkout\\.pickupContact\\.selfPickupContact\\.nationalIdSelf\\.nationalIdSelf");
                        }
                        idLast4Input.waitFor(new Locator.WaitForOptions()
                                .setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
                        idLast4Input.fill(last4);
                        logger.info("已填写身份证后四位");
                        page.waitForTimeout(300);
                    } else {
                        logger.warn("未能获取身份证后四位，跳过该项填写");
                    }
                } catch (Exception ex) {
                    logger.warn("填写身份证后四位时出现异常: {}", ex.getMessage());
                }
            }

            Locator receiptRadio = page
                    .locator("input[name='checkout.pickupContact.eFapiaoSelector.selectFapiao'][value='none']");
            receiptRadio
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(15000));
            if (!receiptRadio.isChecked()) {
                receiptRadio.check();
            }

            Locator continueButton = page.getByRole(AriaRole.BUTTON,
                    new Page.GetByRoleOptions().setName("继续选择付款方式"));
            continueButton
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            continueButton.scrollIntoViewIfNeeded();
            continueButton.click();
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("取货信息填写完毕，已进入付款方式选择");

            selectPaymentMethod(page);

        } catch (Exception e) {
            logger.warn("填写取货信息失败: {}", e.getMessage(), e);
        }
    }

    private void selectPaymentMethod(Page page) {
        try {
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            Locator alipayLabel = page.locator("label[for='checkout.billing.billingoptions.alipay']");
            alipayLabel
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));

            Locator alipayRadio = page.locator("input#checkout\\.billing\\.billingoptions\\.alipay");
            alipayRadio.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(20000));

            if (!alipayRadio.isChecked()) {
                try {
                    alipayRadio.check();
                } catch (Exception e) {
                    logger.debug("直接选中支付宝失败，尝试点击标签: {}", e.getMessage());
                    try {
                        alipayLabel.click();
                    } catch (Exception ignore) {
                        alipayLabel.click(new Locator.ClickOptions().setForce(true));
                    }
                    page.waitForTimeout(200);

                    if (!alipayRadio.isChecked()) {
                        page.evaluate(
                                "radio => {\n                            radio.checked = true;\n                            radio.dispatchEvent(new Event('input', { bubbles: true }));\n                            radio.dispatchEvent(new Event('change', { bubbles: true }));\n                        }",
                                alipayRadio);
                        page.waitForTimeout(200);
                    }
                }
            }

            if (!alipayRadio.isChecked()) {
                logger.warn("支付宝支付方式仍未选中，请人工确认。");
            }

            Locator reviewButton = page.locator("#rs-checkout-continue-button-bottom");
            reviewButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            reviewButton.scrollIntoViewIfNeeded();
            try {
                reviewButton.click();
            } catch (Exception ignore) {
                reviewButton.click(new Locator.ClickOptions().setForce(true));
            }

            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("已选择支付宝并点击检查订单");

            // 调用新的提交最终订单方法
            submitFinalOrder(page);

        } catch (Exception e) {
            logger.warn("选择付款方式失败: {}", e.getMessage(), e);
        }
    }

    private void ensureLocationSelectorOpen(Page page, Locator locationToggle) {
        locationToggle.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
        String expanded = locationToggle.getAttribute("aria-expanded");
        if (!"true".equalsIgnoreCase(expanded)) {
            locationToggle.scrollIntoViewIfNeeded();
            try {
                locationToggle.click();
            } catch (Exception ignore) {
                locationToggle.click(new Locator.ClickOptions().setForce(true));
            }
            page.waitForTimeout(200);
        }

        // 保存位置信息到浏览器存储
        try {
            Locator saveLocationCheckbox = page.getByLabel("保存我的位置信息以便日后使用");
            saveLocationCheckbox.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.ATTACHED)
                    .setTimeout(10000));
            if (!saveLocationCheckbox.isChecked()) {
                saveLocationCheckbox.check(new Locator.CheckOptions().setForce(true));
                logger.info("已勾选'保存我的位置信息以便日后使用'");
            }

            // 等待位置信息保存到浏览器存储
            page.waitForTimeout(2000);

            // 更新用户的会话文件，保存地区选择信息
            updateUserSessionWithLocation();

        } catch (Exception e) {
            logger.warn("保存位置信息失败，但继续执行: {}", e.getMessage());
        }

    }

    private void selectRegionOption(Page page, Locator locationToggle, String panelName, String optionSelector,
            String optionText) {
        ensureLocationSelectorOpen(page, locationToggle);

        Locator targetTab = getRegionTab(page, panelName);
        if (targetTab == null) {
            logger.warn("未找到地区级别选项卡: {}", panelName);
            return;
        }

        targetTab.scrollIntoViewIfNeeded();
        String selectedAttr = targetTab.getAttribute("aria-selected");
        if (!"true".equalsIgnoreCase(selectedAttr)) {
            try {
                targetTab.click();
            } catch (Exception ignore) {
                targetTab.click(new Locator.ClickOptions().setForce(true));
            }
            page.waitForTimeout(200);
        }

        Locator activePanel = page.locator(
                ".rc-province-selector-tab-panel[name='" + panelName + "'][data-core-tabs-panel-selected]");
        activePanel.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

        Locator selectedOption = activePanel
                .locator(optionSelector + ".rc-province-selector-option-selected")
                .filter(new Locator.FilterOptions().setHasText(optionText));

        Locator optionNode = activePanel.locator(optionSelector)
                .filter(new Locator.FilterOptions().setHasText(optionText))
                .first();
        optionNode.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
        optionNode.scrollIntoViewIfNeeded();
        try {
            optionNode.click();
        } catch (Exception ignore) {
            optionNode.click(new Locator.ClickOptions().setForce(true));
        }

        selectedOption.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
        page.waitForTimeout(300);
    }

    private void selectStoreOption(Page page, String storeName, String storeValue) {
        logger.info("🏪 开始选择门店: {} (value: {})", storeName, storeValue);
        page.waitForTimeout(1000);

        Locator storeContainer = page.locator("div.rs-store-locator");
        storeContainer.waitFor(new Locator.WaitForOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(20000));

        int totalStores = storeContainer.locator(".form-selector-input").count();
        logger.info("📊 门店容器已加载，总门店数量: {}", totalStores);

        // 等待门店列表加载
        Locator storeList = storeContainer.locator(".rt-storelocator-store-location");
        storeList.first().waitFor(new Locator.WaitForOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(20000));

        boolean selected = false;

        // 方法1：通过 value 属性直接定位（最有效的方法）
        if (storeValue != null && !storeValue.isEmpty()) {
            logger.info("🎯 方法1：通过 value='{}' 定位门店", storeValue);
            Locator radioByValue = storeContainer.locator("input.form-selector-input[value='" + storeValue + "']");
            int matchCount = radioByValue.count();
            logger.info("📋 匹配到的门店数量: {}", matchCount);

            if (matchCount > 0) {
                try {
                    Locator targetRadio = radioByValue.first();
                    targetRadio.waitFor(
                            new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(20000));

                    String radioId = targetRadio.getAttribute("id");
                    boolean disabled = targetRadio.isDisabled();
                    boolean checked = targetRadio.isChecked();
                    logger.info("✅ 找到目标门店: id='{}', disabled={}, checked={}", radioId, disabled, checked);

                    if (!disabled) {
                        selected = checkStoreRadio(page, targetRadio, storeName, storeValue);
                        logger.info("📊 方法1选择结果: {}", selected ? "成功" : "失败");
                    } else {
                        logger.warn("⚠️ 门店不可用: {} ({})", storeName, storeValue);
                    }
                } catch (PlaywrightException e) {
                    logger.warn("❌ 方法1失败: {}", e.getMessage());
                }
            } else {
                logger.warn("❌ 未找到 value='{}' 的门店", storeValue);
            }
        } else {
            logger.info("ℹ️ 未提供 storeValue，跳过方法1");
        }

        if (selected) {
            logger.info("🎉 门店选择成功！");
            page.waitForTimeout(500);
            return;
        }

        // 如果方法1失败，记录错误信息
        logger.error("❌ 门店选择失败: {} (value: {})", storeName, storeValue);
        logger.info("💡 建议检查门店配置或网络连接");
    }

    private boolean checkStoreRadio(Page page, Locator radio, String storeName, String storeValue) {
        if (radio.isDisabled() || !radio.isEnabled()) {
            logger.debug("单选框无法点击，id={}, disabled={}, enabled={}", radio.getAttribute("id"),
                    radio.isDisabled(), radio.isEnabled());
            return false;
        }

        if (!radio.isChecked()) {
            try {
                radio.scrollIntoViewIfNeeded();
                radio.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(200);
            } catch (Exception clickEx) {
                logger.debug("radio.click(force) 失败: {}", clickEx.getMessage());
            }
        }

        if (!radio.isChecked()) {
            String radioId = radio.getAttribute("id");
            if (radioId != null && !radioId.isEmpty()) {
                Locator label = page.locator("label[for='" + radioId + "']");
                logger.debug("radio 未选中，尝试点击对应标签，label 数量={}", label.count());
                if (label.count() > 0) {
                    try {
                        label.first().click(new Locator.ClickOptions().setForce(true));
                    } catch (Exception ignore) {
                        logger.warn("点击门店标签失败: {}", ignore.getMessage());
                    }
                    page.waitForTimeout(200);
                    if (!radio.isChecked()) {
                        try {
                            page.evaluate(
                                    "({ id }) => { const label = document.querySelector(`label[for='${id}']`); if (label) label.click(); }",
                                    Map.of("id", radioId));
                            page.waitForTimeout(200);
                        } catch (Exception evalLabelEx) {
                            logger.debug("evaluate label.click() 失败: {}", evalLabelEx.getMessage());
                        }
                    }
                }
            }
        }

        if (!radio.isChecked()) {
            logger.debug("标签点击后仍未选中，尝试 evaluate 设置 checked，id={}", radio.getAttribute("id"));
            try {
                ElementHandle handle = radio.elementHandle();
                if (handle != null) {
                    handle.evaluate(
                            "el => {\n                el.checked = true;\n                el.dispatchEvent(new Event('input', { bubbles: true }));\n                el.dispatchEvent(new Event('change', { bubbles: true }));\n            }");
                } else if (storeValue != null) {
                    logger.debug("radio.elementHandle() 返回 null，尝试根据 value 设置");
                    page.evaluate(
                            "({ value }) => { const input = document.querySelector(`input.form-selector-input[value='${value}']`); if (input) { input.checked = true; input.dispatchEvent(new Event('input', { bubbles: true })); input.dispatchEvent(new Event('change', { bubbles: true })); } }",
                            Map.of("value", storeValue));
                }
            } catch (Exception evalEx) {
                logger.debug("evaluate 设置 checked 失败: {}", evalEx.getMessage());
            }
            page.waitForTimeout(200);
        }

        boolean checked = radio.isChecked();
        logger.debug("最终单选框状态，id={}，checked={}", radio.getAttribute("id"), checked);
        if (!checked) {
            logger.warn("门店单选框仍未选中，storeName={}, storeValue={}, id={}", storeName, storeValue,
                    radio.getAttribute("id"));
        }
        return checked;
    }

    private Locator getRegionTab(Page page, String panelName) {
        int index;
        switch (panelName) {
            case "state":
                index = 0;
                break;
            case "city":
                index = 1;
                break;
            case "district":
                index = 2;
                break;
            default:
                logger.warn("未知的地区级别: {}", panelName);
                return null;
        }

        Locator tabs = page.locator(".rc-province-selector-tablist button[role='tab']");
        try {
            tabs.nth(index)
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
        } catch (Exception e) {
            logger.warn("地区级别选项卡未展示: {}", panelName, e);
            return null;
        }
        if (tabs.count() <= index) {
            logger.warn("地区级别选项卡数量不足，无法定位 {}", panelName);
            return null;
        }
        return tabs.nth(index);
    }

    /**
     * 更新用户会话文件，保存地区选择信息
     * 在多线程模式下，将当前的浏览器状态（包括地区信息）保存到用户的会话文件中
     */
    private void updateUserSessionWithLocation() {
        // 只在多线程模式下更新会话文件
        if (page != null && userConfig != null) {
            try {
                // 获取当前页面的浏览器上下文
                BrowserContext context = page.context();

                // 获取用户会话文件路径
                String username = userConfig.getUsername();
                Path userSessionFile = Paths.get("users", username, "apple-auth.json");

                // 保存更新后的会话状态
                saveSessionState(context, userSessionFile);
                logger.info("已更新用户 {} 的会话文件，包含地区选择信息", username);

            } catch (Exception e) {
                logger.warn("更新用户会话文件失败: {}", e.getMessage(), e);
            }
        } else {
            logger.debug("非多线程模式或缺少用户配置，跳过会话文件更新");
        }
    }

    /**
     * 保存浏览器会话状态到文件
     *
     * @param context     浏览器上下文
     * @param storageFile 保存文件路径
     */
    private void saveSessionState(BrowserContext context, Path storageFile) {
        try {
            // 使用SessionFileManager安全地保存会话状态，保留现有配置
            boolean success = com.crawler.util.SessionFileManager.saveSessionStateWithConfigPreservation(context,
                    storageFile);

            if (success) {
                // 智能确保httpStockChecker配置存在
                String storeCode = "R670"; // 默认门店代码
                String buyUrl = "https://www.apple.com.cn/shop/buy-iphone/iphone-17"; // 默认购买URL
                
                boolean configEnsured = com.crawler.util.SessionFileManager.ensureHttpStockCheckerConfig(storageFile, storeCode, buyUrl);
                if (configEnsured) {
                    logger.info("会话文件httpStockChecker配置已确保存在");
                } else {
                    logger.warn("会话文件httpStockChecker配置确保失败，但会话状态已保存");
                }
            } else {
                logger.warn("使用SessionFileManager保存失败，尝试传统方式保存");
                // 回退到传统方式
                fallbackSaveSessionState(context, storageFile);
            }

        } catch (Exception e) {
            logger.warn("保存会话状态失败: {}", e.getMessage(), e);
            // 尝试回退方式
            try {
                fallbackSaveSessionState(context, storageFile);
            } catch (Exception fallbackException) {
                logger.error("回退保存方式也失败: {}", fallbackException.getMessage(), fallbackException);
            }
        }
    }

    /**
     * 回退的会话状态保存方法（保持原有逻辑作为备用）
     */
    private void fallbackSaveSessionState(BrowserContext context, Path storageFile) throws Exception {
        // 确保父目录存在
        if (storageFile.getParent() != null) {
            Files.createDirectories(storageFile.getParent());
        }

        // 保存会话状态
        context.storageState(new BrowserContext.StorageStateOptions().setPath(storageFile));
        logger.info("会话状态已保存到: {}", storageFile.toAbsolutePath());

        // 增强auth文件，添加HttpStockCheckerFromFile所需的配置
        enhanceAuthFile(storageFile);
    }

    private void captureDebugScreenshot(Page page, String suffix) {
        saveScreenshot(page, "apple-debug-" + suffix, "调试截图已保存到: {}");
    }

    private void saveScreenshot(Page page, String filePrefix, String logTemplate) {
        try {
            File dir = new File(screenshotDir);
            if (!dir.exists() && dir.mkdirs()) {
                logger.info("创建截图目录: {}", dir.getAbsolutePath());
            }

            if (!dir.exists()) {
                logger.warn("截图目录不存在且无法创建: {}", dir.getAbsolutePath());
                return;
            }

            String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date());
            Path screenshotPath = Paths.get(dir.getAbsolutePath(), filePrefix + "-" + timestamp + ".png");
            page.waitForTimeout(500);
            page.screenshot(new Page.ScreenshotOptions().setPath(screenshotPath).setFullPage(true));
            logger.info(logTemplate, screenshotPath);
        } catch (Exception e) {
            logger.error("保存截图失败 [{}]: {}", filePrefix, e.getMessage(), e);
        }
    }

    /**
     * 检查页面是否是登录页面
     * 通过检测是否存在登录iframe来判断
     */
    private boolean isLoginPage(Page page) {
        try {
            // 首先检查页面是否还在加载中
            if (page.isClosed()) {
                logger.warn("页面已关闭，无法检查登录状态");
                return false;
            }

            // 检查URL是否包含登录相关路径（最可靠的方法）
            String currentUrl = page.url();
            if (currentUrl.contains("/signIn") || currentUrl.contains("/auth/") ||
                    currentUrl.contains("appleid.apple.com") || currentUrl.contains("idmsa.apple.com")) {
                logger.info("检测到登录URL: {}", currentUrl);
                return true;
            }

            // 检查是否存在登录iframe（需要等待页面稳定）
            try {
                Locator loginIframe = page.locator("iframe#aid-auth-widget-iFrame");
                int iframeCount = loginIframe.count();
                if (iframeCount > 0) {
                    logger.info("检测到登录iframe，当前页面是登录页面");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("检查登录iframe时出错: {}", e.getMessage());
            }

            // 检查是否包含登录相关的文本
            try {
                if (page.locator("text=登录你的 Apple").count() > 0 ||
                        page.locator("text=登录以继续").count() > 0 ||
                        page.locator("text=Apple 账户").count() > 0 ||
                        page.locator("text=Sign in").count() > 0) {
                    logger.info("检测到登录相关文本，当前页面是登录页面");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("检查登录文本时出错: {}", e.getMessage());
            }

            logger.debug("未检测到登录页面特征，继续正常流程");
            return false;

        } catch (Exception e) {
            logger.warn("检查登录页面时出错: {}", e.getMessage());
            // 如果出现执行上下文错误，返回false让程序继续
            return false;
        }
    }

    private boolean isTargetLocation(Locator locationToggle, String... tokens) {
        try {
            locationToggle
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            Locator firstSpan = locationToggle.locator("span");
            String textContent = null;
            if (firstSpan.count() > 0) {
                textContent = firstSpan.first().innerText();
            }
            if (textContent == null || textContent.isEmpty()) {
                textContent = locationToggle.innerText();
            }
            if (textContent != null) {
                for (String token : tokens) {
                    if (!textContent.contains(token)) {
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            logger.debug("读取当前位置文案失败，继续执行地区选择: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 获取当前用户配置
     */
    private UserProfileManager.UserProfile getCurrentUserProfile() {
        if (username != null) {
            return userProfileManager.loadUserProfile(username);
        }
        return null;
    }

    /**
     * 提交最终订单
     * 根据配置决定是否真正提交订单，方便调试
     */
    private void submitFinalOrder(Page page) {
        try {
            // 新逻辑：优先使用根级 finalOrderSubmissionEnabled；若未配置则兼容旧
            // tasks.finalOrderSubmission.enabled
            UserProfileManager.UserProfile currentProfile = getCurrentUserProfile();
            Boolean allowSubmit = null;
            if (currentProfile != null) {
                allowSubmit = currentProfile.getFinalOrderSubmissionEnabled();
                if (allowSubmit == null && currentProfile.getTasks() != null
                        && currentProfile.getTasks().getFinalOrderSubmission() != null) {
                    allowSubmit = currentProfile.getTasks().getFinalOrderSubmission().isEnabled();
                }
            }

            if (allowSubmit != null && !allowSubmit) {
                logger.info("用户 {} 最终订单提交已禁用，跳过提交步骤", username);
                captureAccountScreenshot(page, "end-order-skipped");
                return;
            }

            logger.info("用户 {} 开始提交最终订单", username);

            Locator placeOrderButton = page.locator("#rs-checkout-continue-button-bottom",
                    new Page.LocatorOptions().setHasText("立即下单"));
            placeOrderButton
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(30000));
            placeOrderButton.scrollIntoViewIfNeeded();
            try {
                placeOrderButton.click();
            } catch (Exception ignore) {
                placeOrderButton.click(new Locator.ClickOptions().setForce(true));
            }

            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("用户 {} 已点击立即下单", username);
            captureAccountScreenshot(page, "end-order");

            // 触发“下单成功日志”消息（非阻塞，失败不影响流程）
            try {
                if (messageQueue != null && username != null) {
                    var params = new java.util.HashMap<String, Object>();
                    params.put("user", username);
                    params.put("url", page.url());
                    params.put("ts", System.currentTimeMillis());
                    CrawlerMessage logMsg = CrawlerMessage.createOrderSubmittedLog(
                            username,
                            userConfig,
                            iphoneConfig,
                            pickupLocationConfig,
                            params);
                    boolean offered = messageQueue.offer(logMsg);
                    if (!offered) {
                        logger.warn("将下单日志消息加入队列失败，用户: {}", username);
                    } else {
                        logger.info("已提交下单日志消息到队列，用户: {}", username);
                    }
                }
            } catch (Exception mqEx) {
                logger.warn("提交下单日志消息时发生异常: {}", mqEx.getMessage());
            }

        } catch (Exception e) {
            logger.warn("提交最终订单失败: {}", e.getMessage(), e);
            captureAccountScreenshot(page, "end-order-failed");
        }
    }

    private void captureAccountScreenshot(Page page, String suffix) {
        saveScreenshot(page, "apple-account-" + suffix, "登录后的页面截图已保存到: {}");
    }

    /**
     * 增强auth文件，添加HttpStockCheckerFromFile所需的配置
     *
     * @param authFile auth文件路径
     */
    private void enhanceAuthFile(Path authFile) {
        try {
            // 如果没有捕获到headers，尝试主动获取
            if (capturedFulfillmentHeaders == null || capturedFulfillmentHeaders.isEmpty()) {
                logger.info("未捕获到fulfillment-messages请求headers，尝试主动获取...");
                captureStockCheckHeaders();
            }

            // 读取现有的auth文件
            String content = Files.readString(authFile);
            JsonNode rootNode = objectMapper.readTree(content);

            // 创建httpStockChecker配置
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();

            // 使用配置中的信息构建URL
            String storeCode = pickupLocationConfig.getStoreCode();
            String modelPart = iphoneConfig.getModel().equals("iphone-17") ? "MYEV3CH/A" : "MYEV3CH/A"; // 根据实际型号调整
            String stockCheckUrl = String.format(
                    "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=%s&little=false&parts.0=%s&mts.0=regular&mts.1=sticky&fts=true",
                    storeCode, modelPart);

            httpStockCheckerNode.put("url", stockCheckUrl);
            httpStockCheckerNode.put("referer", iphoneConfig.getBuyUrl());
            httpStockCheckerNode.put("userAgent",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 添加headers
            ObjectNode headersNode = objectMapper.createObjectNode();

            // 使用捕获的headers，如果有的话
            if (capturedFulfillmentHeaders != null && !capturedFulfillmentHeaders.isEmpty()) {
                logger.info("使用捕获的fulfillment-messages请求headers");
                // 复制所有捕获的headers
                capturedFulfillmentHeaders.forEach(headersNode::put);
            } else {
                logger.warn("仍未获取到fulfillment-messages请求headers，使用默认值");
                // 使用默认的headers作为fallback
                headersNode.put("Accept", "*/*");
                headersNode.put("Accept-Encoding", "gzip, deflate, br, zstd");
                headersNode.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
                headersNode.put("Sec-Ch-Ua",
                        "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"");
                headersNode.put("Sec-Ch-Ua-Mobile", "?0");
                headersNode.put("Sec-Ch-Ua-Platform", "\"macOS\"");
                headersNode.put("Sec-Fetch-Dest", "empty");
                headersNode.put("Sec-Fetch-Mode", "cors");
                headersNode.put("Sec-Fetch-Site", "same-origin");
                headersNode.put("Priority", "u=1, i");
                headersNode.put("x-aos-ui-fetch-call-1", generateRandomFetchCallId()); // 生成随机ID
                headersNode.put("x-skip-redirect", "true");
            }
            httpStockCheckerNode.set("headers", headersNode);

            // 将httpStockChecker配置添加到根节点
            if (rootNode instanceof ObjectNode) {
                ((ObjectNode) rootNode).set("httpStockChecker", httpStockCheckerNode);

                // 写回文件
                Files.writeString(authFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
                logger.info("已为HttpStockCheckerFromFile添加配置到: {}", authFile.toAbsolutePath());
            }

        } catch (Exception e) {
            logger.warn("增强auth文件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 主动访问库存页面以捕获必要的headers
     */
    private void captureStockCheckHeaders() {
        try {
            logger.info("主动访问库存页面以捕获headers...");

            // 设置网络监听器来捕获fulfillment-messages请求
            page.onRequest(request -> {
                String url = request.url();
                if (url.contains("fulfillment-messages")) {
                    Map<String, String> headers = request.headers();
                    if (headers.containsKey("x-aos-ui-fetch-call-1")) {
                        capturedFulfillmentHeaders = headers;
                        logger.info("成功捕获fulfillment-messages请求headers，x-aos-ui-fetch-call-1: {}",
                                headers.get("x-aos-ui-fetch-call-1"));
                    }
                }
            });

            // 访问购买页面
            page.navigate(iphoneConfig.getBuyUrl());
            page.waitForLoadState(LoadState.NETWORKIDLE);

            // 等待一下让网络请求完成
            page.waitForTimeout(2000);

            logger.info("完成库存页面访问，headers捕获状态: {}",
                    capturedFulfillmentHeaders != null ? "成功" : "失败");

        } catch (Exception e) {
            logger.warn("主动捕获headers失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成随机的fetch call ID
     */
    private String generateRandomFetchCallId() {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();

        // 生成格式: xxxxxxxxxx-xxxxxxxxxx (10-10)
        for (int i = 0; i < 10; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        sb.append("-");
        for (int i = 0; i < 10; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }

        return sb.toString();
    }

}
