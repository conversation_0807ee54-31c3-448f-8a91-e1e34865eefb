package com.crawler.apple;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.ApplePickupLocationConfig;
import com.crawler.config.AppleUserConfig;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.pool.ChannelHealthChecker;
import io.netty.channel.pool.ChannelPoolHandler;
import io.netty.channel.pool.FixedChannelPool;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.*;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.util.CharsetUtil;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.Promise;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLException;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于 Netty HTTP 的 Apple 业务流程处理器
 * 提供登录、购物车操作等功能的 HTTP 实现版本
 */
public class NettyHttpBusinessFlow {

    private static final Logger logger = LoggerFactory.getLogger(NettyHttpBusinessFlow.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    // 全局 EventLoopGroup（复用）
    private static final NioEventLoopGroup EVENT_LOOP_GROUP = new NioEventLoopGroup(
            Math.max(2, Runtime.getRuntime().availableProcessors()));

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                EVENT_LOOP_GROUP.shutdownGracefully(0, 3, TimeUnit.SECONDS).syncUninterruptibly();
            } catch (Exception ignored) {
            }
        }));
    }

    // 连接池缓存
    private static final Map<InetSocketAddress, FixedChannelPool> POOLS = new ConcurrentHashMap<>();

    // 性能监控计数器
    private static final AtomicLong totalRequests = new AtomicLong(0);

    private final String username;
    private final AppleUserConfig userConfig;
    private final AppleIphoneConfig iphoneConfig;
    private final ApplePickupLocationConfig pickupLocationConfig;

    public NettyHttpBusinessFlow(String username, AppleUserConfig userConfig,
                                 AppleIphoneConfig iphoneConfig, ApplePickupLocationConfig pickupLocationConfig) {
        this.username = username;
        this.userConfig = userConfig;
        this.iphoneConfig = iphoneConfig;
        this.pickupLocationConfig = pickupLocationConfig;
    }

    /**
     * 验证会话是否有效
     */
    public CompletableFuture<Boolean> validateSession(Path sessionFile) {
        try {
            if (!Files.exists(sessionFile)) {
                return CompletableFuture.completedFuture(false);
            }

            // 读取会话文件并验证cookie
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            JsonNode cookiesNode = rootNode.path("cookies");

            if (cookiesNode.isMissingNode() || !cookiesNode.isArray()) {
                return CompletableFuture.completedFuture(false);
            }

            // 检查关键cookie是否存在且未过期
            boolean hasValidCookies = false;
            for (JsonNode cookie : cookiesNode) {
                String name = cookie.path("name").asText();
                if ("myacinfo".equals(name) || "dslang".equals(name)) {
                    // 检查cookie是否过期
                    long expires = cookie.path("expires").asLong(-1);
                    if (expires == -1 || expires > System.currentTimeMillis() / 1000) {
                        hasValidCookies = true;
                        break;
                    }
                }
            }

            return CompletableFuture.completedFuture(hasValidCookies);

        } catch (Exception e) {
            logger.error("验证会话文件失败: {}", e.getMessage(), e);
            return CompletableFuture.completedFuture(false);
        }
    }

    /**
     * 执行简单的HTTP请求
     */
    public CompletableFuture<HttpResponse> performHttpRequest(String url, String method,
                                                              Map<String, String> headers, String body, Path sessionFile) {
        totalRequests.incrementAndGet();
        try {
            URI uri = URI.create(url);
            boolean ssl = "https".equalsIgnoreCase(uri.getScheme());
            int port = uri.getPort() > 0 ? uri.getPort() : (ssl ? 443 : 80);
            String host = Objects.requireNonNull(uri.getHost(), "URL 缺少 host");

            return performRequest(host, port, ssl, uri, method, headers, body, sessionFile);

        } catch (Exception e) {
            CompletableFuture<HttpResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    private CompletableFuture<HttpResponse> performRequest(String host, int port, boolean ssl, URI uri,
                                                           String method, Map<String, String> headers, String body, Path sessionFile) {
        try {
            InetSocketAddress address = new InetSocketAddress(host, port);
            FixedChannelPool pool = POOLS.computeIfAbsent(address, key -> createPool(host, port, ssl));

            CompletableFuture<HttpResponse> outer = new CompletableFuture<>();
            Future<Channel> acquireFuture = pool.acquire();

            acquireFuture.addListener(f -> {
                if (!f.isSuccess()) {
                    outer.completeExceptionally(f.cause());
                    return;
                }
                Channel ch = (Channel) acquireFuture.getNow();

                Promise<HttpResponse> promise = ch.eventLoop().newPromise();
                ChannelInboundHandler responseHandler = new SimpleChannelInboundHandler<FullHttpResponse>() {
                    @Override
                    protected void channelRead0(ChannelHandlerContext ctx, FullHttpResponse msg) {
                        ByteBuf content = msg.content();
                        String responseBody = content.toString(CharsetUtil.UTF_8);
                        int status = msg.status().code();
                        promise.trySuccess(new HttpResponse(status, responseBody, Instant.now()));
                    }

                    @Override
                    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
                        promise.tryFailure(cause);
                    }
                };

                ch.pipeline().addLast("respHandler-" + System.identityHashCode(responseHandler), responseHandler);

                // 构建HTTP请求
                String path = uri.getRawPath() == null || uri.getRawPath().isEmpty() ? "/" : uri.getRawPath();
                if (uri.getRawQuery() != null && !uri.getRawQuery().isEmpty()) {
                    path += "?" + uri.getRawQuery();
                }

                HttpMethod httpMethod = HttpMethod.valueOf(method.toUpperCase());
                DefaultFullHttpRequest req = body != null && !body.isEmpty() ?
                        new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, httpMethod, path,
                                ch.alloc().buffer().writeBytes(body.getBytes(CharsetUtil.UTF_8))) :
                        new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, httpMethod, path);

                HttpHeaders reqHeaders = req.headers();
                reqHeaders.set(HttpHeaderNames.HOST, host);
                reqHeaders.set(HttpHeaderNames.CONNECTION, HttpHeaderValues.KEEP_ALIVE);

                // 添加自定义headers
                if (headers != null) {
                    for (Map.Entry<String, String> entry : headers.entrySet()) {
                        reqHeaders.set(entry.getKey(), entry.getValue());
                    }
                }

                // 添加会话cookie
                try {
                    String cookieHeader = buildCookieHeader(sessionFile);
                    if (cookieHeader != null && !cookieHeader.isEmpty()) {
                        reqHeaders.set(HttpHeaderNames.COOKIE, cookieHeader);
                    }
                } catch (Exception e) {
                    logger.warn("构建cookie header失败: {}", e.getMessage());
                }

                ChannelFuture writeFuture = ch.writeAndFlush(req);

                // 设置超时
                ch.eventLoop().schedule(() -> {
                    if (!promise.isDone()) {
                        promise.tryFailure(new java.util.concurrent.TimeoutException("request timeout"));
                    }
                }, 45, TimeUnit.SECONDS);

                promise.addListener(p -> {
                    try {
                        ch.pipeline().remove(responseHandler);
                    } catch (Exception ignored) {
                    }

                    if (p.isSuccess()) {
                        outer.complete((HttpResponse) p.get());
                    } else {
                        outer.completeExceptionally(p.cause());
                    }

                    pool.release(ch);
                });

                writeFuture.addListener(wf -> {
                    if (!wf.isSuccess()) {
                        promise.tryFailure(wf.cause());
                    }
                });
            });

            return outer;

        } catch (Exception e) {
            CompletableFuture<HttpResponse> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    private String buildCookieHeader(Path sessionFile) throws IOException {
        if (!Files.exists(sessionFile)) {
            return null;
        }

        String content = Files.readString(sessionFile);
        JsonNode rootNode = objectMapper.readTree(content);
        JsonNode cookiesNode = rootNode.path("cookies");

        if (cookiesNode.isMissingNode() || !cookiesNode.isArray()) {
            return null;
        }

        StringBuilder cookieBuilder = new StringBuilder();
        for (JsonNode cookie : cookiesNode) {
            String name = cookie.path("name").asText();
            String value = cookie.path("value").asText();
            if (!name.isEmpty() && !value.isEmpty()) {
                if (cookieBuilder.length() > 0) {
                    cookieBuilder.append("; ");
                }
                cookieBuilder.append(name).append("=").append(value);
            }
        }

        return cookieBuilder.toString();
    }

    private FixedChannelPool createPool(String host, int port, boolean ssl) {
        try {
            final SslContext sslCtx = ssl ? SslContextBuilder.forClient().build() : null;

            Bootstrap bootstrap = new Bootstrap()
                    .group(EVENT_LOOP_GROUP)
                    .channel(NioSocketChannel.class)
                    .remoteAddress(host, port)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 8_000)  // 减少连接超时到8秒
                    .option(ChannelOption.SO_SNDBUF, 1048576)  // 发送缓冲区1MB
                    .option(ChannelOption.SO_RCVBUF, 1048576)  // 接收缓冲区1MB
                    .option(ChannelOption.ALLOCATOR, io.netty.buffer.UnpooledByteBufAllocator.DEFAULT);  // 使用内存池

            ChannelPoolHandler poolHandler = new ChannelPoolHandler() {
                @Override
                public void channelReleased(Channel ch) {
                }

                @Override
                public void channelAcquired(Channel ch) {
                }

                @Override
                public void channelCreated(Channel ch) {
                    ChannelPipeline p = ch.pipeline();
                    if (sslCtx != null) {
                        p.addLast("ssl", sslCtx.newHandler(ch.alloc(), host, port));
                    }
                    p.addLast("codec", new HttpClientCodec());
                    p.addLast("decompressor", new HttpContentDecompressor());
                    p.addLast("aggregator", new HttpObjectAggregator(5 * 1024 * 1024));
                    p.addLast("readTimeout", new ReadTimeoutHandler(30));
                }
            };

            return new FixedChannelPool(
                    bootstrap,
                    poolHandler,
                    ChannelHealthChecker.ACTIVE,
                    FixedChannelPool.AcquireTimeoutAction.FAIL,
                    5_000,
                    50,
                    200,
                    true
            );
        } catch (SSLException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取性能统计信息
     */
    public static Map<String, Long> getPerformanceStats() {
        Map<String, Long> stats = new java.util.HashMap<>();
        stats.put("totalRequests", totalRequests.get());
        stats.put("poolSize", (long) POOLS.size());
        return stats;
    }

    /**
     * HTTP响应封装类
     */
    public static class HttpResponse {
        private final int status;
        private final String body;
        private final Instant timestamp;

        public HttpResponse(int status, String body, Instant timestamp) {
            this.status = status;
            this.body = body;
            this.timestamp = timestamp;
        }

        public int getStatus() {
            return status;
        }

        public String getBody() {
            return body;
        }

        public Instant getTimestamp() {
            return timestamp;
        }
    }
}
