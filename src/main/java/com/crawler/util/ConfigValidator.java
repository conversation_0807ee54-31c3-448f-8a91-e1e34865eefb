package com.crawler.util;

import org.springframework.stereotype.Component;

/**
 * 配置验证工具类
 * 用于验证配置文件中的中文字符是否正确读取
 */
@Component
public class ConfigValidator {
    
    /**
     * 验证中文字符是否正确
     * @param value 要验证的值
     * @param expected 期望的值
     * @return 是否匹配
     */
    public static boolean validateChineseText(String value, String expected) {
        if (value == null || expected == null) {
            return false;
        }
        
        // 检查是否包含乱码字符
        boolean hasGarbledChars = value.matches(".*[\\u00C0-\\u00FF].*");
        if (hasGarbledChars) {
            System.err.println("检测到乱码字符: " + value);
            return false;
        }
        
        return value.equals(expected);
    }
    
    /**
     * 打印字符的Unicode编码
     * @param text 要分析的文本
     */
    public static void printUnicodeInfo(String text) {
        if (text == null) {
            System.out.println("文本为null");
            return;
        }
        
        System.out.println("文本: " + text);
        System.out.println("长度: " + text.length());
        System.out.println("字节数组: " + java.util.Arrays.toString(text.getBytes()));
        
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            System.out.println("字符[" + i + "]: " + c + " (Unicode: \\u" + Integer.toHexString(c).toUpperCase() + ")");
        }
    }
}
