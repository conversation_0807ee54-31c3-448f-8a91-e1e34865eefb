package com.crawler.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.microsoft.playwright.BrowserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 会话文件管理器
 * 提供线程安全的会话文件操作，确保配置不会在更新过程中丢失
 */
public class SessionFileManager {
    
    private static final Logger logger = LoggerFactory.getLogger(SessionFileManager.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 为每个文件路径提供独立的锁
    private static final ConcurrentHashMap<String, ReentrantLock> fileLocks = new ConcurrentHashMap<>();
    
    /**
     * 安全地保存会话状态，同时保留现有的自定义配置
     * 
     * @param context 浏览器上下文
     * @param sessionFile 会话文件路径
     * @return 是否保存成功
     */
    public static boolean saveSessionStateWithConfigPreservation(BrowserContext context, Path sessionFile) {
        String filePath = sessionFile.toAbsolutePath().toString();
        ReentrantLock lock = fileLocks.computeIfAbsent(filePath, k -> new ReentrantLock());
        
        lock.lock();
        try {
            logger.info("开始安全保存会话状态到: {}", sessionFile);
            
            // 1. 备份现有配置（包括所有自定义配置）
            JsonNode existingHttpStockChecker = null;
            JsonNode existingCustomConfigs = null;
            if (Files.exists(sessionFile)) {
                try {
                    String content = Files.readString(sessionFile);
                    JsonNode rootNode = objectMapper.readTree(content);
                    
                    // 备份httpStockChecker配置
                    existingHttpStockChecker = rootNode.path("httpStockChecker");
                    
                    // 备份其他自定义配置（除了cookies和origins）
                    if (rootNode instanceof ObjectNode) {
                        ObjectNode customConfigs = objectMapper.createObjectNode();
                        rootNode.fieldNames().forEachRemaining(fieldName -> {
                            if (!"cookies".equals(fieldName) && !"origins".equals(fieldName)) {
                                customConfigs.set(fieldName, rootNode.get(fieldName));
                            }
                        });
                        if (customConfigs.size() > 0) {
                            existingCustomConfigs = customConfigs;
                        }
                    }
                    
                    logger.debug("已备份现有配置: httpStockChecker={}, 其他配置={}", 
                        existingHttpStockChecker != null && !existingHttpStockChecker.isMissingNode(),
                        existingCustomConfigs != null);
                } catch (Exception e) {
                    logger.warn("备份现有配置失败，将继续保存: {}", e.getMessage());
                }
            }
            
            // 2. 确保父目录存在
            if (sessionFile.getParent() != null) {
                Files.createDirectories(sessionFile.getParent());
            }
            
            // 3. 保存新的会话状态到临时文件
            Path tempFile = sessionFile.resolveSibling(sessionFile.getFileName() + ".tmp");
            context.storageState(new BrowserContext.StorageStateOptions().setPath(tempFile));
            
            // 4. 合并所有现有配置到新文件中
            if (existingCustomConfigs != null) {
                mergeCustomConfigs(tempFile, existingCustomConfigs);
            } else if (existingHttpStockChecker != null && !existingHttpStockChecker.isMissingNode()) {
                // 兼容旧版本：只合并httpStockChecker
                mergeHttpStockCheckerConfig(tempFile, existingHttpStockChecker);
            }
            
            // 5. 原子性地替换原文件
            Files.move(tempFile, sessionFile, StandardCopyOption.REPLACE_EXISTING);
            
            // 6. 验证配置完整性
            boolean isValid = validateSessionFile(sessionFile);
            if (!isValid) {
                logger.warn("会话文件验证失败，但保存操作已完成: {}", sessionFile);
            }
            
            logger.info("✅ 会话状态已安全保存到: {} (保留httpStockChecker配置)", sessionFile);
            return true;
            
        } catch (Exception e) {
            logger.error("保存会话状态失败: {}", e.getMessage(), e);
            return false;
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 将httpStockChecker配置合并到会话文件中
     */
    private static void mergeHttpStockCheckerConfig(Path sessionFile, JsonNode httpStockCheckerConfig) {
        try {
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            
            if (rootNode instanceof ObjectNode) {
                ObjectNode objectNode = (ObjectNode) rootNode;
                objectNode.set("httpStockChecker", httpStockCheckerConfig);
                
                String updatedContent = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(rootNode);
                Files.writeString(sessionFile, updatedContent);
                
                logger.debug("已合并httpStockChecker配置到会话文件");
            }
        } catch (Exception e) {
            logger.warn("合并httpStockChecker配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 将自定义配置合并到会话文件中
     */
    private static void mergeCustomConfigs(Path sessionFile, JsonNode customConfigs) {
        try {
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            
            if (rootNode instanceof ObjectNode && customConfigs instanceof ObjectNode) {
                ObjectNode objectNode = (ObjectNode) rootNode;
                ObjectNode customNode = (ObjectNode) customConfigs;
                
                // 合并所有自定义配置
                customNode.fieldNames().forEachRemaining(fieldName -> {
                    objectNode.set(fieldName, customNode.get(fieldName));
                });
                
                String updatedContent = objectMapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(rootNode);
                Files.writeString(sessionFile, updatedContent);
                
                logger.debug("已合并自定义配置到会话文件: {}", customNode.fieldNames());
            }
        } catch (Exception e) {
            logger.warn("合并自定义配置失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 验证会话文件的完整性
     */
    private static boolean validateSessionFile(Path sessionFile) {
        try {
            if (!Files.exists(sessionFile)) {
                logger.warn("会话文件不存在: {}", sessionFile);
                return false;
            }
            
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            
            // 检查基本结构
            boolean hasCookies = rootNode.has("cookies") && rootNode.get("cookies").isArray();
            boolean hasOrigins = rootNode.has("origins") && rootNode.get("origins").isArray();
            
            if (!hasCookies || !hasOrigins) {
                logger.warn("会话文件缺少基本结构 (cookies/origins): {}", sessionFile);
                return false;
            }
            
            logger.debug("会话文件验证通过: {}", sessionFile);
            return true;
            
        } catch (Exception e) {
            logger.warn("验证会话文件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 安全地增强会话文件，添加httpStockChecker配置
     * 
     * @param sessionFile 会话文件路径
     * @param storeCode 门店代码
     * @param buyUrl 购买页面URL
     * @return 是否增强成功
     */
    public static boolean enhanceSessionFileWithStockChecker(Path sessionFile, String storeCode, String buyUrl) {
        String filePath = sessionFile.toAbsolutePath().toString();
        ReentrantLock lock = fileLocks.computeIfAbsent(filePath, k -> new ReentrantLock());
        
        lock.lock();
        try {
            if (!Files.exists(sessionFile)) {
                logger.error("会话文件不存在: {}", sessionFile);
                return false;
            }
            
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            
            // 检查是否已经有httpStockChecker配置
            if (rootNode.has("httpStockChecker")) {
                logger.info("会话文件已包含httpStockChecker配置: {}", sessionFile);
                return true;
            }
            
            if (!(rootNode instanceof ObjectNode)) {
                logger.error("会话文件格式不正确: {}", sessionFile);
                return false;
            }
            
            ObjectNode objectNode = (ObjectNode) rootNode;
            
            // 创建httpStockChecker配置
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            
            // 构建库存检查URL
            String modelPart = "MYEV3CH/A"; // 默认iPhone型号，可以根据需要调整
            String stockCheckUrl = String.format(
                "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=%s&little=false&parts.0=%s&mts.0=regular&mts.1=sticky&fts=true",
                storeCode != null ? storeCode : "R670", modelPart);
            
            httpStockCheckerNode.put("url", stockCheckUrl);
            httpStockCheckerNode.put("referer", buyUrl != null ? buyUrl : "https://www.apple.com.cn/shop/buy-iphone/iphone-16");
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            
            // 创建基本的headers结构
            ObjectNode headersNode = objectMapper.createObjectNode();
            headersNode.put("Accept", "application/json, text/javascript, */*; q=0.01");
            headersNode.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            headersNode.put("Cache-Control", "no-cache");
            headersNode.put("Pragma", "no-cache");
            headersNode.put("Sec-Fetch-Dest", "empty");
            headersNode.put("Sec-Fetch-Mode", "cors");
            headersNode.put("Sec-Fetch-Site", "same-origin");
            headersNode.put("X-Requested-With", "XMLHttpRequest");
            headersNode.put("x-skip-redirect", "true");
            
            httpStockCheckerNode.set("headers", headersNode);
            objectNode.set("httpStockChecker", httpStockCheckerNode);
            
            // 保存更新后的文件
            String updatedContent = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(rootNode);
            Files.writeString(sessionFile, updatedContent);
            
            logger.info("✅ 已为会话文件添加httpStockChecker配置: {}", sessionFile);
            return true;
            
        } catch (Exception e) {
            logger.error("增强会话文件失败: {}", e.getMessage(), e);
            return false;
        } finally {
            lock.unlock();
        }
    }
    
    /**
     * 检查会话文件是否包含httpStockChecker配置
     */
    public static boolean hasHttpStockCheckerConfig(Path sessionFile) {
        try {
            if (!Files.exists(sessionFile)) {
                return false;
            }
            
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);
            
            return rootNode.has("httpStockChecker") && 
                   rootNode.get("httpStockChecker").isObject() &&
                   rootNode.get("httpStockChecker").has("url");
                   
        } catch (Exception e) {
            logger.warn("检查httpStockChecker配置时发生异常: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 智能增强会话文件，确保httpStockChecker配置存在且有效
     * 如果配置缺失或无效，会自动添加默认配置
     */
    public static boolean ensureHttpStockCheckerConfig(Path sessionFile, String storeCode, String buyUrl) {
        String filePath = sessionFile.toAbsolutePath().toString();
        ReentrantLock lock = fileLocks.computeIfAbsent(filePath, k -> new ReentrantLock());
        
        lock.lock();
        try {
            if (!Files.exists(sessionFile)) {
                logger.error("会话文件不存在: {}", sessionFile);
                return false;
            }
            
            // 检查现有配置是否有效
            if (hasHttpStockCheckerConfig(sessionFile)) {
                logger.debug("会话文件已包含有效的httpStockChecker配置: {}", sessionFile);
                return true;
            }
            
            logger.info("会话文件缺少httpStockChecker配置，正在添加: {}", sessionFile);
            return enhanceSessionFileWithStockChecker(sessionFile, storeCode, buyUrl);
            
        } catch (Exception e) {
            logger.error("确保httpStockChecker配置时发生异常: {}", e.getMessage(), e);
            return false;
        } finally {
            lock.unlock();
        }
    }
}
