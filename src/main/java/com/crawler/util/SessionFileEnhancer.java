package com.crawler.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 会话文件增强工具
 * 用于为现有的会话文件添加httpStockChecker配置
 */
public class SessionFileEnhancer {

    private static final Logger logger = LoggerFactory.getLogger(SessionFileEnhancer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 为指定用户的会话文件添加httpStockChecker配置
     */
    public static boolean enhanceSessionFile(String username, String storeCode, String buyUrl) {
        try {
            Path sessionFile = Paths.get("users", username, "apple-auth.json");
            
            if (!Files.exists(sessionFile)) {
                logger.error("会话文件不存在: {}", sessionFile);
                return false;
            }

            // 读取现有的auth文件
            String content = Files.readString(sessionFile);
            JsonNode rootNode = objectMapper.readTree(content);

            // 检查是否已经有httpStockChecker配置
            if (rootNode.has("httpStockChecker")) {
                logger.info("会话文件已包含httpStockChecker配置: {}", sessionFile);
                return true;
            }

            // 创建httpStockChecker配置
            ObjectNode httpStockCheckerNode = objectMapper.createObjectNode();
            
            // 使用传入的参数构建URL
            String modelPart = "MYEV3CH/A"; // iPhone 17 Pro的部件号
            String stockCheckUrl = String.format(
                "https://www.apple.com.cn/shop/fulfillment-messages?fae=true&store=%s&little=false&parts.0=%s&mts.0=regular&mts.1=sticky&fts=true",
                storeCode, modelPart);
            
            httpStockCheckerNode.put("url", stockCheckUrl);
            httpStockCheckerNode.put("referer", buyUrl);
            httpStockCheckerNode.put("userAgent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

            // 添加headers
            ObjectNode headersNode = objectMapper.createObjectNode();
            headersNode.put("Accept", "*/*");
            headersNode.put("Accept-Encoding", "gzip, deflate, br, zstd");
            headersNode.put("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            headersNode.put("Sec-Ch-Ua", "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"HeadlessChrome\";v=\"134\"");
            headersNode.put("Sec-Ch-Ua-Mobile", "?0");
            headersNode.put("Sec-Ch-Ua-Platform", "\"macOS\"");
            headersNode.put("Sec-Fetch-Dest", "empty");
            headersNode.put("Sec-Fetch-Mode", "cors");
            headersNode.put("Sec-Fetch-Site", "same-origin");
            headersNode.put("Priority", "u=1, i");
            headersNode.put("x-aos-ui-fetch-call-1", generateRandomFetchCallId());
            headersNode.put("x-skip-redirect", "true");
            
            httpStockCheckerNode.set("headers", headersNode);

            // 将httpStockChecker配置添加到根节点
            if (rootNode instanceof ObjectNode) {
                ((ObjectNode) rootNode).set("httpStockChecker", httpStockCheckerNode);

                // 写回文件
                Files.writeString(sessionFile, objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));
                logger.info("已为用户 {} 的会话文件添加httpStockChecker配置: {}", username, sessionFile);
                return true;
            }

        } catch (Exception e) {
            logger.error("增强用户 {} 的会话文件失败: {}", username, e.getMessage(), e);
        }
        
        return false;
    }

    /**
     * 为指定用户的会话文件添加httpStockChecker配置（使用配置文件中的信息）
     */
    public static boolean enhanceSessionFileFromConfig(String username) {
        try {
            Path configFile = Paths.get("users", username, "config.json");
            
            if (!Files.exists(configFile)) {
                logger.error("配置文件不存在: {}", configFile);
                return false;
            }

            // 读取配置文件
            String configContent = Files.readString(configFile);
            JsonNode configNode = objectMapper.readTree(configContent);

            // 提取必要信息
            String storeCode = configNode.path("pickupLocationConfig").path("storeCode").asText("R670");
            String buyUrl = configNode.path("iphoneConfig").path("buyUrl").asText("https://www.apple.com.cn/shop/buy-iphone/iphone-17");

            return enhanceSessionFile(username, storeCode, buyUrl);

        } catch (Exception e) {
            logger.error("从配置文件增强用户 {} 的会话文件失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成随机的fetch call ID
     */
    private static String generateRandomFetchCallId() {
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();
        
        // 生成格式: xxxxxxxxxx-xxxxxxxxxx (10-10)
        for (int i = 0; i < 10; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        sb.append("-");
        for (int i = 0; i < 10; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }

    /**
     * 主方法，用于手动修复会话文件
     */
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("用法: java SessionFileEnhancer <username>");
            System.out.println("示例: <NAME_EMAIL>");
            return;
        }

        String username = args[0];
        logger.info("开始为用户 {} 增强会话文件", username);

        boolean success = enhanceSessionFileFromConfig(username);
        if (success) {
            logger.info("用户 {} 的会话文件增强成功", username);
        } else {
            logger.error("用户 {} 的会话文件增强失败", username);
        }
    }
}
