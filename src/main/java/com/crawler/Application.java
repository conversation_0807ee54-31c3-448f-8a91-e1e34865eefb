package com.crawler;

import com.crawler.apple.AppleLoginCrawler;
import com.crawler.user.UserProfileManager;
import com.crawler.service.MessageQueueService;
import com.crawler.service.StockMonitorService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@SpringBootApplication
public class Application {

    // 移除了SESSIONS_DIR，现在使用用户目录结构

    @Autowired
    private UserProfileManager userProfileManager;

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private StockMonitorService stockMonitorService;

    @Value("${apple.crawler.thread.count:5}")
    private int threadCount;

    @Value("${apple.login.headless:true}")
    private boolean headless;

    @Bean
    public CommandLineRunner runner(AppleLoginCrawler appleLoginCrawler) {
        return args -> {
            // 初始化用户配置文件管理器
            userProfileManager.initialize();

            // 检查命令行参数
            if (args.length > 0) {
                String command = args[0];

                switch (command) {
                    case "login":
                        handleLoginCommand(args, appleLoginCrawler);
                        break;

                    case "users":
                        handleUsersCommand(args);
                        break;

                    case "parallel":
                        // 已废弃：使用 queue 或 server 命令替代
                        System.out.println("❌ parallel 命令已废弃！");
                        System.out.println("请使用以下命令替代：");
                        System.out.println("  java -jar app.jar queue    # 启动消息队列模式");
                        System.out.println("  java -jar app.jar server   # 启动Web服务模式");
                        break;

                    case "queue":
                        // 消息队列模式：启动永远存活的工作线程（命令行模式）
                        handleQueueCommand(args);
                        break;

                    case "server":
                        // Web 服务模式：启动HTTP接口，可通过Controller发送消息
                        handleServerCommand(args);
                        break;

                    case "send":
                        // 发送消息到队列
                        handleSendCommand(args);
                        break;

                    case "status":
                        // 查看队列状态
                        handleStatusCommand();
                        break;

                    case "monitor":
                        // 库存监控命令
                        handleMonitorCommand(args);
                        break;

                    default:
                        System.out.println("未知命令: " + command);
                        printUsage();
                        break;
                }
            } else {
                // 默认启动单线程爬虫程序（保持向后兼容）
                System.out.println("启动单线程爬虫程序...");
                appleLoginCrawler.startSingleMode();
            }
        };
    }

    /**
     * 处理登录命令
     */
    private void handleLoginCommand(String[] args, AppleLoginCrawler appleLoginCrawler) {
        if (args.length < 2) {
            System.out.println("使用方法: java -jar app.jar login <用户名>");
            System.out.println("示例: java -jar app.<NAME_EMAIL>");
            return;
        }

        String username = args[1];
        System.out.println("🔑 为用户 " + username + " 生成会话文件...");
        System.out.println("注意：使用 application.properties 中的 username 和 password 配置进行登录");

        try {
            // 调用 AppleLoginCrawler 的登录方法，传入要保存的用户名目录
            appleLoginCrawler.loginAndGenerateSession(username);
            System.out.println("✅ 会话文件生成完成！");
            System.out.println("会话文件已保存到: users/" + username + "/apple-auth.json");
        } catch (Exception e) {
            System.err.println("❌ 会话文件生成失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理用户管理命令
     */
    private void handleUsersCommand(String[] args) {
        if (args.length < 2) {
            System.out.println("用户管理命令:");
            System.out.println("  list - 列出所有用户");
            System.out.println("  create <用户名> - 创建新用户");
            System.out.println("  delete <用户名> - 删除用户");
            return;
        }

        String subCommand = args[1];
        switch (subCommand) {
            case "list":
                System.out.println("已配置的用户:");
                var profiles = userProfileManager.listUserProfiles();
                if (profiles.isEmpty()) {
                    System.out.println("  无用户配置");
                } else {
                    profiles.forEach(profile -> System.out.println("  " + profile));
                }
                break;

            case "create":
                if (args.length < 3) {
                    System.out.println("使用方法: java -jar app.jar users create <用户名>");
                    return;
                }
                System.out.println("创建用户功能需要进一步实现");
                break;

            case "delete":
                if (args.length < 3) {
                    System.out.println("使用方法: java -jar app.jar users delete <用户名>");
                    return;
                }
                boolean deleted = userProfileManager.deleteUserProfile(args[2]);
                if (deleted) {
                    System.out.println("用户 " + args[2] + " 已删除");
                } else {
                    System.out.println("删除用户失败: " + args[2]);
                }
                break;

            default:
                System.out.println("未知的用户命令: " + subCommand);
                break;
        }
    }

    /**
     * 打印使用说明
     */
    private void printUsage() {
        System.out.println("使用方法:");
        System.out.println("  java -jar app.jar                    # 单线程模式（默认）");
        System.out.println("  java -jar app.jar parallel          # 并发模式");
        System.out.println("  java -jar app.jar queue             # 消息队列模式（控制台）");
        System.out.println("  java -jar app.jar server            # Web服务模式（HTTP控制接口）");
        System.out.println("  java -jar app.jar login <用户名>     # 生成会话文件");
        System.out.println("  java -jar app.jar users list        # 列出所有用户");
        System.out.println("  java -jar app.jar users create <用户名> # 创建用户");
        System.out.println("  java -jar app.jar users delete <用户名> # 删除用户");
        System.out.println("");
        System.out.println("消息队列模式命令:");
        System.out.println("  java -jar app.jar send login <用户名>         # 向用户发送登录消息");
        System.out.println("  java -jar app.jar send login all             # 向所有用户发送登录消息");
        System.out.println("  java -jar app.jar send purchase <用户名>      # 向用户发送购买消息");
        System.out.println("  java -jar app.jar send purchase all          # 向所有用户发送购买消息");
        System.out.println("  java -jar app.jar send refresh <用户名>       # 向用户发送会话刷新消息");
        System.out.println("  java -jar app.jar send refresh all           # 向所有用户发送会话刷新消息");
        System.out.println("  java -jar app.jar send stock <用户名>         # 向用户发送库存检查消息");
        System.out.println("  java -jar app.jar send stock all             # 向所有用户发送库存检查消息");
        System.out.println("  java -jar app.jar status                     # 查看队列状态");
        System.out.println("");
        System.out.println("库存监控命令:");
        System.out.println("  java -jar app.jar monitor start <用户名> <分钟> # 启动定时库存监控");
        System.out.println("  java -jar app.jar monitor stop               # 停止库存监控");
        System.out.println("  java -jar app.jar monitor status             # 查看监控状态");
    }

    /**
     * 旧的并发执行方法 - 已废弃，使用队列模式替代
     */
    @Deprecated
    private void runParallelTasks() throws Exception {
        System.out.println("❌ runParallelTasks 方法已废弃！请使用消息队列模式。");
        System.out.println("示例: java -jar app.jar server");
        // 下面的代码已被注释，因为 CrawlerTask 已删除
        /* 旧代码已删除 */
    }

    /**
     * 处理消息队列模式命令
     */
    private void handleQueueCommand(String[] args) {
        System.out.println("🚀 启动消息队列模式...");
        System.out.println("工作线程数: " + threadCount);
        System.out.println("无头模式: " + headless);

        try {
            // 启动消息队列服务
            messageQueueService.start();

            System.out.println("✅ 消息队列服务已启动，工作线程正在运行");
            System.out.println("💡 使用 'java -jar app.jar send <类型> <用户>' 发送消息");
            System.out.println("💡 使用 'java -jar app.jar status' 查看状态");
            System.out.println("⏳ 按 Ctrl+C 停止服务");

            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                System.out.println("\n🛑 正在关闭消息队列服务...");
                messageQueueService.shutdown();
                System.out.println("✅ 消息队列服务已关闭");
            }));

            // 保持程序运行
            while (true) {
                try {
                    Thread.sleep(10000); // 每10秒打印一次状态
                    var status = messageQueueService.getQueueStatus();
                    if (status != null) {
                        System.out.println(String.format("📊 队列: %d | 工作线程: %d/%d | 已处理: %d | 成功: %d | 失败: %d",
                                status.getQueueSize(),
                                status.getActiveWorkers(),
                                status.getTotalWorkers(),
                                status.getTotalProcessedMessages(),
                                status.getTotalSuccessMessages(),
                                status.getTotalFailedMessages()));
                    }
                } catch (InterruptedException e) {
                    System.out.println("程序被中断");
                    break;
                }
            }

        } catch (Exception e) {
            System.err.println("❌ 启动消息队列模式失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理 Web 服务模式命令
     */
    private void handleServerCommand(String[] args) {
        System.out.println("🚀 启动 Web 服务模式 (HTTP 控制接口)...");
        System.out.println("示例接口:");
        System.out.println("  POST /api/queue/start");
        System.out.println("  GET  /api/queue/status");
        System.out.println("  POST /api/queue/send/stock/{username}");
        System.out.println("  POST /api/monitor/start?username=xx&intervalMinutes=1");
        System.out.println("  GET  /api/monitor/status");
        System.out.println("⏳ 按 Ctrl+C 停止服务");
    }

    /**
     * 处理发送消息命令
     */
    private void handleSendCommand(String[] args) {
        if (args.length < 2) {
            System.out.println("发送消息命令:");
            System.out.println("  send login <用户名>    # 向用户发送登录消息");
            System.out.println("  send login all        # 向所有用户发送登录消息");
            System.out.println("  send purchase <用户名> # 向用户发送购买消息");
            System.out.println("  send purchase all     # 向所有用户发送购买消息");
            System.out.println("  send refresh <用户名>  # 向用户发送会话刷新消息");
            System.out.println("  send refresh all      # 向所有用户发送会话刷新消息");
            System.out.println("  send stock <用户名>    # 向用户发送库存检查消息");
            System.out.println("  send stock all        # 向所有用户发送库存检查消息");
            return;
        }

        String messageType = args[1];

        if (args.length < 3) {
            System.out.println("请指定目标用户名或 'all'");
            return;
        }

        String target = args[2];

        try {
            switch (messageType) {
                case "login":
                    if ("all".equals(target)) {
                        System.out.println("📤 向所有用户发送登录消息...");
                        // 这里需要实现向所有用户发送登录消息的逻辑
                        var profiles = userProfileManager.listUserProfiles();
                        int count = 0;
                        for (var profile : profiles) {
                            if (messageQueueService.sendLoginMessage(profile.getUsername())) {
                                count++;
                            }
                        }
                        System.out.println("✅ 成功发送 " + count + " 个登录消息");
                    } else {
                        System.out.println("📤 向用户 " + target + " 发送登录消息...");
                        if (messageQueueService.sendLoginMessage(target)) {
                            System.out.println("✅ 登录消息已发送");
                        } else {
                            System.out.println("❌ 发送登录消息失败");
                        }
                    }
                    break;

                case "purchase":
                    if ("all".equals(target)) {
                        System.out.println("📤 向所有用户发送购买消息...");
                        int count = messageQueueService.sendPurchaseMessageToAllUsers();
                        System.out.println("✅ 成功发送 " + count + " 个购买消息");
                    } else {
                        System.out.println("📤 向用户 " + target + " 发送购买消息...");
                        if (messageQueueService.sendPurchaseMessage(target)) {
                            System.out.println("✅ 购买消息已发送");
                        } else {
                            System.out.println("❌ 发送购买消息失败");
                        }
                    }
                    break;

                case "refresh":
                    if ("all".equals(target)) {
                        System.out.println("📤 向所有用户发送会话刷新消息...");
                        int count = messageQueueService.sendSessionUpdateMessageToAllUsers();
                        System.out.println("✅ 成功发送 " + count + " 个会话刷新消息");
                    } else {
                        System.out.println("📤 向用户 " + target + " 发送会话刷新消息...");
                        if (messageQueueService.sendSessionUpdateMessage(target)) {
                            System.out.println("✅ 会话刷新消息已发送");
                        } else {
                            System.out.println("❌ 发送会话刷新消息失败");
                        }
                    }
                    break;

                case "stock":
                    if ("all".equals(target)) {
                        System.out.println("📤 向所有用户发送库存检查消息...");
                        int count = messageQueueService.sendStockCheckMessageToAllUsers();
                        System.out.println("✅ 成功发送 " + count + " 个库存检查消息");
                    } else {
                        System.out.println("📤 向用户 " + target + " 发送库存检查消息...");
                        if (messageQueueService.sendStockCheckMessage(target)) {
                            System.out.println("✅ 库存检查消息已发送");
                        } else {
                            System.out.println("❌ 发送库存检查消息失败");
                        }
                    }
                    break;

                default:
                    System.out.println("❌ 不支持的消息类型: " + messageType);
                    break;
            }

        } catch (Exception e) {
            System.err.println("❌ 发送消息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理状态查询命令
     */
    private void handleStatusCommand() {
        try {
            messageQueueService.printQueueStatus();
        } catch (Exception e) {
            System.err.println("❌ 查询状态失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理库存监控命令
     */
    private void handleMonitorCommand(String[] args) {
        if (args.length < 2) {
            System.out.println("库存监控命令:");
            System.out.println("  monitor start <用户名> <分钟>  # 启动定时库存监控");
            System.out.println("  monitor stop                 # 停止库存监控");
            System.out.println("  monitor status               # 查看监控状态");
            return;
        }

        String subCommand = args[1];

        try {
            // 初始化库存监控服务
            stockMonitorService.initialize();

            switch (subCommand) {
                case "start":
                    if (args.length < 4) {
                        System.out.println("使用方法: monitor start <用户名> <分钟>");
                        System.out.println("示例: <NAME_EMAIL> 1");
                        return;
                    }

                    String username = args[2];
                    int intervalMinutes;
                    try {
                        intervalMinutes = Integer.parseInt(args[3]);
                        if (intervalMinutes < 1) {
                            System.out.println("❌ 检查间隔必须大于0分钟");
                            return;
                        }
                    } catch (NumberFormatException e) {
                        System.out.println("❌ 无效的时间间隔: " + args[3]);
                        return;
                    }

                    System.out.println("🚀 启动用户 " + username + " 的库存监控...");
                    System.out.println("检查间隔: " + intervalMinutes + " 分钟");

                    if (stockMonitorService.startStockMonitoring(username, intervalMinutes)) {
                        System.out.println("✅ 库存监控已启动！");
                        System.out.println("💡 使用 'java -jar app.jar monitor stop' 停止监控");
                        System.out.println("💡 使用 'java -jar app.jar monitor status' 查看状态");
                    } else {
                        System.out.println("❌ 启动库存监控失败");
                    }
                    break;

                case "stop":
                    System.out.println("🛑 停止库存监控...");
                    if (stockMonitorService.stopStockMonitoring()) {
                        System.out.println("✅ 库存监控已停止");
                    } else {
                        System.out.println("❌ 停止库存监控失败或当前没有运行中的监控");
                    }
                    break;

                case "status":
                    var status = stockMonitorService.getMonitorStatus();
                    System.out.println("📊 库存监控状态: " + status);
                    break;

                default:
                    System.out.println("❌ 未知的监控命令: " + subCommand);
                    break;
            }

        } catch (Exception e) {
            System.err.println("❌ 执行库存监控命令失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(Application.class);
        // 默认命令行模式；如显式传入 server 则启用 SERVLET 模式
        boolean serverMode = false;
        for (String arg : args) {
            if ("server".equalsIgnoreCase(arg)) {
                serverMode = true;
                break;
            }
        }
        app.setWebApplicationType(serverMode ? WebApplicationType.SERVLET : WebApplicationType.NONE);
        app.run(args);
    }

}
