package com.crawler.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 库存监控服务
 * 为特定用户提供定时库存检查功能
 */
@Service
public class StockMonitorService {
    
    private static final Logger logger = LoggerFactory.getLogger(StockMonitorService.class);
    
    @Autowired
    private MessageQueueService messageQueueService;
    
    private ScheduledExecutorService scheduledExecutorService;
    private ScheduledFuture<?> currentMonitorTask;
    private String currentUsername;
    private volatile boolean isMonitoring = false;

    public void initialize() {
        if (scheduledExecutorService == null) {
            scheduledExecutorService = Executors.newScheduledThreadPool(1, new ThreadFactory() {
                private final AtomicLong threadCounter = new AtomicLong(0);
                
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("StockMonitor-" + threadCounter.incrementAndGet());
                    thread.setDaemon(true);
                    return thread;
                }
            });
            logger.info("库存监控服务初始化完成");
        }
    }

    /**
     * 启动定时库存监控
     * @param username 用户名
     * @param intervalMinutes 检查间隔（分钟）
     */
    public synchronized boolean startStockMonitoring(String username, int intervalMinutes) {
        if (scheduledExecutorService == null) {
            initialize();
        }
        
        // 如果已经在监控，先停止
        if (isMonitoring) {
            logger.info("停止当前用户 {} 的库存监控", currentUsername);
            stopStockMonitoring();
        }
        
        logger.info("启动用户 {} 的定时库存监控，检查间隔: {} 分钟", username, intervalMinutes);
        
        currentUsername = username;
        isMonitoring = true;
        
        // 立即执行一次检查
        messageQueueService.sendStockCheckMessage(username);
        
        // 启动定时任务
        currentMonitorTask = scheduledExecutorService.scheduleAtFixedRate(
                () -> {
                    try {
                        logger.debug("执行定时库存检查 - 用户: {}", username);
                        messageQueueService.sendStockCheckMessage(username);
                    } catch (Exception e) {
                        logger.error("定时库存检查失败 - 用户: {}, 错误: {}", username, e.getMessage(), e);
                    }
                },
                intervalMinutes, // 初始延迟
                intervalMinutes, // 执行间隔
                TimeUnit.MINUTES
        );
        
        logger.info("✅ 用户 {} 的定时库存监控已启动", username);
        return true;
    }

    /**
     * 停止库存监控
     */
    public synchronized boolean stopStockMonitoring() {
        if (!isMonitoring || currentMonitorTask == null) {
            logger.warn("当前没有运行中的库存监控任务");
            return false;
        }
        
        logger.info("停止用户 {} 的库存监控", currentUsername);
        
        currentMonitorTask.cancel(false);
        currentMonitorTask = null;
        isMonitoring = false;
        
        logger.info("✅ 用户 {} 的库存监控已停止", currentUsername);
        String stoppedUser = currentUsername;
        currentUsername = null;
        
        return true;
    }

    /**
     * 手动触发一次库存检查
     */
    public boolean triggerStockCheck(String username) {
        logger.info("手动触发用户 {} 的库存检查", username);
        return messageQueueService.sendStockCheckMessage(username);
    }

    /**
     * 获取当前监控状态
     */
    public MonitorStatus getMonitorStatus() {
        return new MonitorStatus(isMonitoring, currentUsername);
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("关闭库存监控服务...");
        
        if (isMonitoring) {
            stopStockMonitoring();
        }
        
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
            try {
                if (!scheduledExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("库存监控服务已关闭");
    }

    /**
     * 监控状态信息
     */
    public static class MonitorStatus {
        private final boolean isMonitoring;
        private final String currentUsername;

        public MonitorStatus(boolean isMonitoring, String currentUsername) {
            this.isMonitoring = isMonitoring;
            this.currentUsername = currentUsername;
        }

        public boolean isMonitoring() {
            return isMonitoring;
        }

        public String getCurrentUsername() {
            return currentUsername;
        }

        @Override
        public String toString() {
            if (isMonitoring) {
                return "正在监控用户: " + currentUsername;
            } else {
                return "当前未进行库存监控";
            }
        }
    }
}
