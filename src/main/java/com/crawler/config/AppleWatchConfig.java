package com.crawler.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Apple Watch 购买配置。
 */
@Component
@ConfigurationProperties(prefix = "apple.watch")
public class AppleWatchConfig {
    private String buyUrl;
    private String checkoutUrl;

    // 表壳
    private String caseDisplayName;
    private String caseValue;

    // 表带类型
    private String bandStyleDisplayName;
    private String bandStyleValue;

    // 表带颜色
    private String bandColorDisplayName;
    private String bandColorValue;

    // 表带尺码
    private String bandSizeDisplayName;
    private String bandSizeValue;

    private boolean tradeIn;
    private boolean appleCare;

    public String getBuyUrl() {
        return buyUrl;
    }

    public void setBuyUrl(String buyUrl) {
        this.buyUrl = buyUrl;
    }

    public String getCheckoutUrl() {
        return checkoutUrl;
    }

    public void setCheckoutUrl(String checkoutUrl) {
        this.checkoutUrl = checkoutUrl;
    }

    public String getCaseDisplayName() {
        return caseDisplayName;
    }

    public void setCaseDisplayName(String caseDisplayName) {
        this.caseDisplayName = caseDisplayName;
    }

    public String getCaseValue() {
        return caseValue;
    }

    public void setCaseValue(String caseValue) {
        this.caseValue = caseValue;
    }

    public String getBandStyleDisplayName() {
        return bandStyleDisplayName;
    }

    public void setBandStyleDisplayName(String bandStyleDisplayName) {
        this.bandStyleDisplayName = bandStyleDisplayName;
    }

    public String getBandStyleValue() {
        return bandStyleValue;
    }

    public void setBandStyleValue(String bandStyleValue) {
        this.bandStyleValue = bandStyleValue;
    }

    public String getBandColorDisplayName() {
        return bandColorDisplayName;
    }

    public void setBandColorDisplayName(String bandColorDisplayName) {
        this.bandColorDisplayName = bandColorDisplayName;
    }

    public String getBandColorValue() {
        return bandColorValue;
    }

    public void setBandColorValue(String bandColorValue) {
        this.bandColorValue = bandColorValue;
    }

    public String getBandSizeDisplayName() {
        return bandSizeDisplayName;
    }

    public void setBandSizeDisplayName(String bandSizeDisplayName) {
        this.bandSizeDisplayName = bandSizeDisplayName;
    }

    public String getBandSizeValue() {
        return bandSizeValue;
    }

    public void setBandSizeValue(String bandSizeValue) {
        this.bandSizeValue = bandSizeValue;
    }

    public boolean isTradeIn() {
        return tradeIn;
    }

    public void setTradeIn(boolean tradeIn) {
        this.tradeIn = tradeIn;
    }

    public boolean isAppleCare() {
        return appleCare;
    }

    public void setAppleCare(boolean appleCare) {
        this.appleCare = appleCare;
    }
}
