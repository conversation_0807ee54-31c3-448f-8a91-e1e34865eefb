package com.crawler.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Apple取货地点配置类
 * 用于管理取货地点的省、市、区、门店信息
 */
@Component
@ConfigurationProperties(prefix = "apple.pickup.location")
public class ApplePickupLocationConfig {
    
    // 省份信息
    private String province;
    private String provinceDisplayName;
    
    // 城市信息
    private String city;
    private String cityDisplayName;
    
    // 区域信息
    private String district;
    private String districtDisplayName;
    
    // 门店信息
    private String storeName;
    private String storeCode;
    private String storeDisplayName;
    
    // 默认构造函数
    public ApplePickupLocationConfig() {}
    
    // Getters and Setters
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getProvinceDisplayName() {
        return provinceDisplayName;
    }
    
    public void setProvinceDisplayName(String provinceDisplayName) {
        this.provinceDisplayName = provinceDisplayName;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCityDisplayName() {
        return cityDisplayName;
    }
    
    public void setCityDisplayName(String cityDisplayName) {
        this.cityDisplayName = cityDisplayName;
    }
    
    public String getDistrict() {
        return district;
    }
    
    public void setDistrict(String district) {
        this.district = district;
    }
    
    public String getDistrictDisplayName() {
        return districtDisplayName;
    }
    
    public void setDistrictDisplayName(String districtDisplayName) {
        this.districtDisplayName = districtDisplayName;
    }
    
    public String getStoreName() {
        return storeName;
    }
    
    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    
    public String getStoreCode() {
        return storeCode;
    }
    
    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }
    
    public String getStoreDisplayName() {
        return storeDisplayName;
    }
    
    public void setStoreDisplayName(String storeDisplayName) {
        this.storeDisplayName = storeDisplayName;
    }
    
    @Override
    public String toString() {
        return "ApplePickupLocationConfig{" +
                "province='" + province + '\'' +
                ", provinceDisplayName='" + provinceDisplayName + '\'' +
                ", city='" + city + '\'' +
                ", cityDisplayName='" + cityDisplayName + '\'' +
                ", district='" + district + '\'' +
                ", districtDisplayName='" + districtDisplayName + '\'' +
                ", storeName='" + storeName + '\'' +
                ", storeCode='" + storeCode + '\'' +
                ", storeDisplayName='" + storeDisplayName + '\'' +
                '}';
    }
}
