package com.crawler.controller;

import com.crawler.service.MessageQueueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/queue")
public class QueueController {

	@Autowired
	private MessageQueueService messageQueueService;

	@PostMapping("/start")
	public ResponseEntity<?> start() {
		messageQueueService.start();
		return ResponseEntity.ok("queue started");
	}

	@GetMapping("/status")
	public ResponseEntity<?> status() {
		var status = messageQueueService.getQueueStatus();
		return ResponseEntity.ok(status);
	}

	@PostMapping("/send/login/{username}")
	public ResponseEntity<?> sendLogin(@PathVariable String username) {
		boolean ok = messageQueueService.sendLoginMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/purchase/{username}")
	public ResponseEntity<?> sendPurchase(@PathVariable String username) {
		boolean ok = messageQueueService.sendPurchaseMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/cart/{username}")
	public ResponseEntity<?> sendAddToCart(@PathVariable String username) {
		boolean ok = messageQueueService.sendAddToCartMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/watch-cart/{username}")
	public ResponseEntity<?> sendAddWatchToCart(@PathVariable String username) {
		boolean ok = messageQueueService.sendAddWatchToCartMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/clear-bag/{username}")
	public ResponseEntity<?> sendClearBag(@PathVariable String username) {
		boolean ok = messageQueueService.sendClearBagMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/refresh/{username}")
	public ResponseEntity<?> sendRefresh(@PathVariable String username) {
		boolean ok = messageQueueService.sendSessionUpdateMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}

	@PostMapping("/send/stock/{username}")
	public ResponseEntity<?> sendStock(@PathVariable String username) {
		boolean ok = messageQueueService.sendStockCheckMessage(username);
		return ResponseEntity.ok(ok ? "sent" : "failed");
	}
}
