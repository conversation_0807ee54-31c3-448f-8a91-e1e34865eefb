package com.crawler.controller;

import com.crawler.service.StockMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/monitor")
public class MonitorController {

	@Autowired
	private StockMonitorService stockMonitorService;

	@PostMapping("/start")
	public ResponseEntity<?> start(@RequestParam String username, @RequestParam(defaultValue = "1") int intervalMinutes) {
		stockMonitorService.initialize();
		boolean ok = stockMonitorService.startStockMonitoring(username, intervalMinutes);
		return ResponseEntity.ok(ok ? "started" : "failed");
	}

	@PostMapping("/stop")
	public ResponseEntity<?> stop() {
		boolean ok = stockMonitorService.stopStockMonitoring();
		return ResponseEntity.ok(ok ? "stopped" : "no-running-task");
	}

	@GetMapping("/status")
	public ResponseEntity<?> status() {
		return ResponseEntity.ok(stockMonitorService.getMonitorStatus());
	}
}


