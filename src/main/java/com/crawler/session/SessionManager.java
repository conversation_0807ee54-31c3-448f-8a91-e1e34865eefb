package com.crawler.session;

import com.crawler.apple.AppleLoginCrawler;
import com.crawler.config.AppleUserConfig;
import com.crawler.config.AppleIphoneConfig;
import com.crawler.user.UserProfileManager;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.ColorScheme;
import com.microsoft.playwright.options.Geolocation;
import com.microsoft.playwright.options.LoadState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

/**
 * 会话管理器
 * 负责生成和管理Apple账号的会话文件
 */
@Component
public class SessionManager {

    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);

    @Autowired
    private UserProfileManager userProfileManager;
    
    /**
     * 为指定用户名生成会话文件（从用户配置文件加载）
     *
     * @param username 用户名
     * @return 是否生成成功
     */
    public boolean generateSession(String username) {
        logger.info("开始为用户 {} 生成会话文件", username);

        // 从用户配置文件加载用户信息
        UserProfileManager.UserProfile profile = userProfileManager.loadUserProfile(username);
        if (profile == null) {
            logger.error("用户配置文件不存在: {}", username);
            return false;
        }

        return generateSessionWithProfile(profile);
    }

    /**
     * 使用用户配置文件生成会话文件
     *
     * @param profile 用户配置文件
     * @return 是否生成成功
     */
    public boolean generateSessionWithProfile(UserProfileManager.UserProfile profile) {
        logger.info("开始为账号 {} 生成会话文件: {}", profile.getUsername(), profile.getDescription());

        String username = profile.getUsername();
        Path sessionFile = userProfileManager.getSessionFilePath(username);

        // 确保用户目录存在
        try {
            Path userDir = sessionFile.getParent();
            if (!Files.exists(userDir)) {
                Files.createDirectories(userDir);
                logger.info("创建用户目录: {}", userDir.toAbsolutePath());
            }
        } catch (Exception e) {
            logger.error("创建用户目录失败: {}", e.getMessage(), e);
            return false;
        }

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;

        try {
            playwright = Playwright.create();

            // 启动浏览器
            browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                .setHeadless(false) // 生成会话时使用非headless模式以便调试
                .setArgs(Arrays.asList(
                    "--disable-blink-features=AutomationControlled",
                    "--start-maximized",
                    "--disable-webrtc"
                ))
                .setTimeout(120000)
            );

            // 创建浏览器上下文
            context = browser.newContext(new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .setGeolocation(new Geolocation(39.9042, 116.4074)) // 北京位置
                .setPermissions(Arrays.asList("geolocation"))
                .setColorScheme(ColorScheme.LIGHT)
            );

            page = context.newPage();

            // 执行登录流程
            AppleLoginCrawler crawler = new AppleLoginCrawler(page, profile.getUserConfig(), profile.getIphoneConfig());

            // 导航到Apple购买页面并执行登录
            logger.info("开始执行登录流程...");
            AppleIphoneConfig iphoneConfig = profile.getIphoneConfig();
            String buyUrl = iphoneConfig != null && iphoneConfig.getBuyUrl() != null ?
                iphoneConfig.getBuyUrl() : "https://www.apple.com.cn/shop/buy-iphone/iphone-16";
            page.navigate(buyUrl);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            // 这里可以添加更复杂的登录逻辑
            // 暂时让用户手动登录
            logger.info("请在浏览器中手动完成登录，然后按回车键继续...");
            System.out.println("请在浏览器中手动完成登录，然后按回车键继续...");
            System.in.read(); // 等待用户输入

            // 保存会话状态
            context.storageState(new BrowserContext.StorageStateOptions().setPath(sessionFile));

            logger.info("会话文件已生成: {}", sessionFile.toAbsolutePath());
            System.out.println("会话文件已生成: " + sessionFile.toAbsolutePath());

            return true;

        } catch (Exception e) {
            logger.error("生成会话文件失败: {}", e.getMessage(), e);
            return false;
        } finally {
            // 清理资源
            if (page != null) {
                try {
                    page.close();
                } catch (Exception e) {
                    logger.warn("关闭页面时出错: {}", e.getMessage());
                }
            }

            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }

            if (browser != null) {
                try {
                    browser.close();
                } catch (Exception e) {
                    logger.warn("关闭浏览器时出错: {}", e.getMessage());
                }
            }

            if (playwright != null) {
                try {
                    playwright.close();
                } catch (Exception e) {
                    logger.warn("关闭Playwright时出错: {}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 检查会话文件是否存在
     */
    public boolean sessionExists(String username) {
        return userProfileManager.userSessionExists(username);
    }
    
    /**
     * 删除会话文件
     */
    public boolean deleteSession(String username) {
        Path sessionFile = userProfileManager.getSessionFilePath(username);
        try {
            boolean deleted = Files.deleteIfExists(sessionFile);
            if (deleted) {
                logger.info("会话文件已删除: {}", sessionFile);
            }
            return deleted;
        } catch (Exception e) {
            logger.error("删除会话文件失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 列出所有会话文件
     */
    public void listSessions() {
        try {
            var profiles = userProfileManager.listUserProfiles();
            System.out.println("用户会话文件状态:");
            
            for (var profile : profiles) {
                String username = profile.getUsername();
                boolean hasSession = userProfileManager.userSessionExists(username);
                String status = hasSession ? "✅ 已生成" : "❌ 未生成";
                System.out.println("  " + username + " - " + status);
            }
                
        } catch (Exception e) {
            logger.error("列出会话文件失败: {}", e.getMessage(), e);
        }
    }
}
