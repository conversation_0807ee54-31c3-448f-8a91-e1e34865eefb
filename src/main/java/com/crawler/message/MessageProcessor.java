package com.crawler.message;

/**
 * 消息处理器接口
 * 负责处理不同类型的爬虫任务消息
 */
public interface MessageProcessor {
    
    /**
     * 处理消息
     * @param message 待处理的消息
     * @return 处理结果
     */
    MessageResult processMessage(CrawlerMessage message);
    
    /**
     * 检查是否支持处理该类型的消息
     * @param messageType 消息类型
     * @return 是否支持
     */
    boolean supports(CrawlerMessage.MessageType messageType);
    
    /**
     * 获取处理器名称
     * @return 处理器名称
     */
    String getProcessorName();
}
