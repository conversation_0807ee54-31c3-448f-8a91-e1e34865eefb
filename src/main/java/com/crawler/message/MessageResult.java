package com.crawler.message;

import java.time.LocalDateTime;

/**
 * 消息处理结果
 */
public class MessageResult {
    
    /**
     * 处理状态枚举
     */
    public enum Status {
        /** 成功 */
        SUCCESS,
        /** 失败 */
        FAILED,
        /** 需要重试 */
        RETRY,
        /** 跳过 */
        SKIPPED
    }
    
    /** 处理状态 */
    private final Status status;
    
    /** 结果消息 */
    private final String message;
    
    /** 异常信息 */
    private final Throwable throwable;
    
    /** 处理开始时间 */
    private final LocalDateTime startTime;
    
    /** 处理结束时间 */
    private final LocalDateTime endTime;
    
    /** 处理器名称 */
    private final String processorName;

    private MessageResult(Builder builder) {
        this.status = builder.status;
        this.message = builder.message;
        this.throwable = builder.throwable;
        this.startTime = builder.startTime;
        this.endTime = builder.endTime;
        this.processorName = builder.processorName;
    }

    // Getters
    public Status getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public Throwable getThrowable() {
        return throwable;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public String getProcessorName() {
        return processorName;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }

    /**
     * 是否需要重试
     */
    public boolean shouldRetry() {
        return status == Status.RETRY;
    }

    /**
     * 获取处理耗时（毫秒）
     */
    public long getProcessingTimeMillis() {
        if (startTime == null || endTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, endTime).toMillis();
    }

    @Override
    public String toString() {
        return "MessageResult{" +
                "status=" + status +
                ", message='" + message + '\'' +
                ", processorName='" + processorName + '\'' +
                ", processingTime=" + getProcessingTimeMillis() + "ms" +
                '}';
    }

    /**
     * Builder 模式构造器
     */
    public static class Builder {
        private Status status;
        private String message;
        private Throwable throwable;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String processorName;

        public Builder status(Status status) {
            this.status = status;
            return this;
        }

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder throwable(Throwable throwable) {
            this.throwable = throwable;
            return this;
        }

        public Builder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder endTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder processorName(String processorName) {
            this.processorName = processorName;
            return this;
        }

        public MessageResult build() {
            if (status == null) {
                throw new IllegalArgumentException("状态不能为空");
            }
            return new MessageResult(this);
        }
    }

    /**
     * 创建成功结果的便捷方法
     */
    public static MessageResult success(String message, String processorName) {
        return new Builder()
                .status(Status.SUCCESS)
                .message(message)
                .processorName(processorName)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果的便捷方法
     */
    public static MessageResult failed(String message, Throwable throwable, String processorName) {
        return new Builder()
                .status(Status.FAILED)
                .message(message)
                .throwable(throwable)
                .processorName(processorName)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建重试结果的便捷方法
     */
    public static MessageResult retry(String message, String processorName) {
        return new Builder()
                .status(Status.RETRY)
                .message(message)
                .processorName(processorName)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建跳过结果的便捷方法
     */
    public static MessageResult skipped(String message, String processorName) {
        return new Builder()
                .status(Status.SKIPPED)
                .message(message)
                .processorName(processorName)
                .endTime(LocalDateTime.now())
                .build();
    }
}
