./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"

java -jar target/applecrawler-0.0.1-SNAPSHOT.jar server

curl -X POST http://localhost:8080/api/queue/start       

curl -X POST "http://localhost:8080/api/queue/send/login/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/watch-cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/purchase/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/clear-bag/<EMAIL>"



"iphoneConfig": {
        "buyUrl": "https://www.apple.com.cn/shop/buy-iphone/iphone-17",
        "checkoutUrl": "https://www.apple.com.cn/shop/bag",
        "model": "iphone-17",
        "modelDisplayName": "iPhone 17",
        "color": "black",
        "colorDisplayName": "薰衣草紫色",
        "storage": "256gb",
        "storageDisplayName": "256GB 脚注",
        "tradeIn": false,
        "appleCare": false,
        "checkinIndicatorText": "需要签到",
        "requiresModelSelection": false,
        "alternateColors": [
            {
                "color": "sage-green",
                "colorDisplayName": "鼠尾草绿色"
            },
            {
                "color": "mist-blue",
                "colorDisplayName": "青雾蓝色"
            },
            {
                "color": "white",
                "colorDisplayName": "白色"
            },
            {
                "color": "black",
                "colorDisplayName": "黑色"
            }
        ]
    },


    "pickupLocationConfig": {
        "province": "yunnan",
        "provinceDisplayName": "四川",
        "city": "kunming",
        "cityDisplayName": "成都",
        "district": "wuhua",
        "districtDisplayName": "武侯区",
        "storeName": "Apple 成都太古里",
        "storeCode": "R580",
        "storeDisplayName": "Apple 成都太古里"
    }


        "watchConfig": {
        "buyUrl": "https://www.apple.com.cn/shop/buy-watch/apple-watch-ultra/49mm-cellular-black-titanium-black-alpine-loop-small",
        "checkoutUrl": "https://www.apple.com.cn/shop/bag",
        "caseDisplayName": "黑色",
        "caseValue": "black",
        "bandStyleDisplayName": "高山回环式表带",
        "bandStyleValue": "alpineloop",
        "bandColorDisplayName": "黑色",
        "bandColorValue": "black",
        "bandSizeDisplayName": "S",
        "bandSizeValue": "small",
        "tradeIn": false,
        "appleCare": false
    },


https://secure10.www.apple.com.cn/shop/checkout?_s=Fulfillment-init

https://secure10.www.apple.com.cn/shop/checkout?_s=PickupContact-init

https://secure10.www.apple.com.cn/shop/checkout?_s=Billing-init

https://secure10.www.apple.com.cn/shop/checkout?_s=Review

https://secure8.www.apple.com.cn/shop/checkoutx/review?_a=continueFromReviewToProcess&_m=checkout.review.placeOrder








https://secure10.www.apple.com.cn/shop/checkout/interstitial










2025-09-28T09:55:16.893+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 开始监控 iPhone 17 Pro 昆明库存...
2025-09-28T09:55:46.893+08:00 ERROR 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : iPhone 17 Pro 选购流程执行失败: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"


com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.WaitableResult.get(WaitableResult.java:52) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:132) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:118) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.lambda$navigate$47(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:97) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.Page.navigate(Page.java:5491) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.crawler.apple.AppleBusinessFlow.checkIphone17ProStock(AppleBusinessFlow.java:228) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processStockCheck(CrawlerWorker.java:463) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processMessage(CrawlerWorker.java:222) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.run(CrawlerWorker.java:160) ~[!/:0.0.1-SNAPSHOT]
        at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
        at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
        at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.Connection.dispatch(Connection.java:254) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.processOneMessage(Connection.java:211) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        ... 18 common frames omitted

2025-09-28T09:55:46.894+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 尝试切换颜色并检查库存: 白色
2025-09-28T09:56:00.692+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 检查是否显示昆明取货信息...
2025-09-28T09:56:00.696+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 检查是否显示昆明取货信息...
2025-09-28T09:56:10.697+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 当前颜色无库存
2025-09-28T09:56:10.697+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> iPhone17Pro暂时无库存
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [ScheduledTask-4] com.crawler.queue.MessageQueueManager    : 消息已提交到队列: 28c5b026-7dca-4a20-a12e-9281db732ff6 (类型: CHECK_IPHONE17PRO_STOCK, 目标: <EMAIL>)
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> 开始处理消息: CHECK_IPHONE17PRO_STOCK
2025-09-28T09:56:16.823+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.worker.CrawlerWorker         : 用户 <EMAIL> 开始检查iPhone17Pro库存
2025-09-28T09:56:16.914+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : ✅ AppleBusinessFlow 初始化完成，用户: <EMAIL>
2025-09-28T09:56:16.914+08:00  INFO 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : 开始监控 iPhone 17 Pro 昆明库存...
2025-09-28T09:56:46.917+08:00 ERROR 44024 --- [crawler] [CrawlerWorker-6] com.crawler.apple.AppleBusinessFlow      : iPhone 17 Pro 选购流程执行失败: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"


com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.WaitableResult.get(WaitableResult.java:52) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:132) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:118) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.lambda$navigate$47(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:97) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:943) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.Page.navigate(Page.java:5491) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.crawler.apple.AppleBusinessFlow.checkIphone17ProStock(AppleBusinessFlow.java:228) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processStockCheck(CrawlerWorker.java:463) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.processMessage(CrawlerWorker.java:222) ~[!/:0.0.1-SNAPSHOT]
        at com.crawler.worker.CrawlerWorker.run(CrawlerWorker.java:160) ~[!/:0.0.1-SNAPSHOT]
        at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572) ~[na:na]
        at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[na:na]
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[na:na]
        at java.base/java.lang.Thread.run(Thread.java:1583) ~[na:na]
Caused by: com.microsoft.playwright.TimeoutError: Error {
  message='Timeout 30000ms exceeded.
  name='TimeoutError
  stack='TimeoutError: Timeout 30000ms exceeded.
    at ProgressController.run (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/progress.js:75:26)
    at Frame.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/frames.js:521:23)
    at FrameDispatcher.goto (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/frameDispatcher.js:81:119)
    at FrameDispatcher._handleCommand (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:96:40)
    at DispatcherConnection.dispatch (/private/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/playwright-java-5077164971247702783/package/lib/server/dispatchers/dispatcher.js:366:39)
}
Call log:
-   - navigating to "https://www.apple.com.cn/shop/buy-iphone/iphone-17", waiting until "load"

        at com.microsoft.playwright.impl.Connection.dispatch(Connection.java:254) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.Connection.processOneMessage(Connection.java:211) ~[playwright-1.51.0.jar!/:1.51.0]
        at com.microsoft.playwright.impl.ChannelOwner.runUntil(ChannelOwner.java:130) ~[playwright-1.51.0.jar!/:1.51.0]
        ... 18 common frames omitted

        页面加载完成超时问题


        集体收到消息，一起打开网页互相拥挤问题

        日志带线程用户帐号ID问题功能

        成功记录流程报错及载图功能

        购买成功记录功能

        重试多次下单功能

        软件加密，限时使用问题


https://secure10.www.apple.com.cn/shop/signIn/idms/authx?ssi=4AAABmZDAyuoBIEp1BdikHOK8sNDB-nTN3IQO_-dHtb1TSDxpZYmjorXNAAAAUmh0dHBzOi8vc2VjdXJlMTAud3d3LmFwcGxlLmNvbS5jbi9zaG9wL2NoZWNrb3V0L3N0YXJ0P3BsdG49MTU5NTgyNzR8fDtNRzhYNDtNWUVWM3wAAgGf7ubwyuVLLJWlTOqNY2I1fsn9SKZEQeb7lup3JF83DQ&up=true

https://secure10.www.apple.com.cn/shop/checkoutx/fulfillment?_a=continueFromFulfillmentToShipping&_m=checkout.fulfillment


(base) [8:00] hujun@:~/Desktop/applecrawer%  cd /Users/<USER>/Desktop/applecrawer ; /usr/bin/env /Users/<USER>/graalvm-jdk-21.0.7+8.1/Contents/Home/bin/
java @/var/folders/gy/5dm712rj53s5fyh2typjz8tm0000gn/T/cp_xvfjp5efx1dq8a78ngq84ie3.argfile com.crawler.apple.http.HttpStartCheckout 
正在发送“开始结账”的 POST 请求...
Final Status Code: 200
Final URL: https://www.apple.com.cn/shop/bagx/checkout_now?_a=checkout&_m=shoppingCart.actions
Location Header: <none>
Set-Cookie Count: 5
Response Body Snippet:
{"head":{"status":302,"data":{"url":"https://secure10.www.apple.com.cn/shop/signIn?ssi=4AAABmZfs_ZABIC6pQ7gO6fdhe-8eiXiZZFaKAYPTNcIoUmYkhkZyy2KfAAAATGh0dHBzOi8vc2VjdXJlMTAud3d3LmFwcGxlLmNvbS5jbi9zaG9wL2NoZWNrb3V0L3N0YXJ0P3BsdG49ODZFQ0RDRTZ8fDtNWUVWM3wAAgG2Wjdgf00vtI0Elkn5V4_nx6ZLNqgEtym-RbiaQtz9ZQ&up=t"}},"body":{}}

❌ 请求失败！请检查：
1. Cookie 字符串是否完整且未过期。
2. 所有请求头是否都已原样复制。
3. POST Body 是否确实为空（请再次检查 trace.zip 的 Body/Payload 标签页）。
4. x-aos-stk 是否为当前会话的最新值（通常会变化）。
5. 如果是 AJAX 流程，可能返回 JSON/HTML 提示或 redirectUrl，请根据 Body Snippet 判断。